/*
 * DemonCrystal - Advanced Crystal Combat System for Skylandia
 *
 * A demonic crystal combat module that unleashes hell upon your enemies with intelligent
 * multi-crystal placement, adaptive targeting, and emergency escape mechanisms.
 *
 * Features:
 * - Multi-Crystal Warfare: Simultaneous placement at multiple strategic locations
 * - Demonic Auto-Pearl: Emergency escape when death approaches
 * - Intelligent damage calculation and prediction
 * - Advanced rotation and timing systems
 * - Comprehensive safety mechanisms
 */

package dev.journey.Skylandia.modules.automation;

import net.minecraft.block.Block;
import com.google.common.util.concurrent.AtomicDouble;
import it.unimi.dsi.fastutil.ints.*;
import meteordevelopment.meteorclient.events.entity.EntityAddedEvent;
import meteordevelopment.meteorclient.events.entity.EntityRemovedEvent;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.events.render.Render2DEvent;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.mixininterface.IBox;
import meteordevelopment.meteorclient.mixininterface.IMiningToolItem;
import meteordevelopment.meteorclient.mixininterface.IRaycastContext;
import meteordevelopment.meteorclient.mixininterface.IVec3d;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.renderer.text.TextRenderer;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.friends.Friends;
import dev.journey.Skylandia.Skylandia;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.entity.DamageUtils;
import meteordevelopment.meteorclient.utils.entity.EntityUtils;
import meteordevelopment.meteorclient.utils.entity.Target;
import meteordevelopment.meteorclient.utils.misc.Keybind;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.player.PlayerUtils;
import meteordevelopment.meteorclient.utils.player.Rotations;
import meteordevelopment.meteorclient.utils.render.NametagUtils;
import meteordevelopment.meteorclient.utils.render.RenderUtils;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.meteorclient.utils.world.BlockIterator;
import meteordevelopment.meteorclient.utils.world.BlockUtils;
import meteordevelopment.meteorclient.utils.world.TickRate;
import meteordevelopment.orbit.EventHandler;
import meteordevelopment.orbit.EventPriority;
import net.minecraft.block.Blocks;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.decoration.EndCrystalEntity;
import net.minecraft.entity.effect.StatusEffectInstance;
import net.minecraft.entity.effect.StatusEffects;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.*;
import net.minecraft.network.packet.c2s.play.*;
import net.minecraft.util.Hand;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.HitResult;
import net.minecraft.util.math.*;
import net.minecraft.util.shape.VoxelShape;
import net.minecraft.world.RaycastContext;
import org.joml.Vector3d;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.io.File;
import java.util.stream.Collectors;

public class DemonCrystal extends Module {
// Register notification handlers (should be done once, e.g. in module init)
static {
    dev.journey.Skylandia.utils.NotificationRegistry.registerHandler(new dev.journey.Skylandia.utils.ToastNotificationHandler());
    dev.journey.Skylandia.utils.NotificationRegistry.registerHandler(new dev.journey.Skylandia.utils.ChatNotificationHandler());
}

    // === SETTING GROUPS ===
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgTargeting = settings.createGroup("Demonic Eyes");
    private final SettingGroup sgPlacement = settings.createGroup("Crystal Placement");
    private final SettingGroup sgMultiCrystal = settings.createGroup("Multi-Crystal Warfare");
    private final SettingGroup sgTrapping = settings.createGroup("Prison Trapping");
    private final SettingGroup sgBreaking = settings.createGroup("Crystal Breaking");
    private final SettingGroup sgAutoPearl = settings.createGroup("Auto-Pearl Escape");
    private final SettingGroup sgSwitching = settings.createGroup("Item Switching");
    private final SettingGroup sgSafety = settings.createGroup("Holy Systems");
    private final SettingGroup sgTiming = settings.createGroup("Timing & Delays");
// === BLOCK PLACEMENT SPEED ===
private final SettingGroup sgBlockPlacement = settings.createGroup("Block Placement");
    private final Setting<Double> blocksPerSecond = sgBlockPlacement.add(new DoubleSetting.Builder()
        .name("blocks-per-second")
        .description("Maximum number of blocks to place per second for all defensive block placement (surround, pillar, trap).")
        .defaultValue(4.0)
        .min(0.5)
        .max(20.0)
        .sliderMax(10.0)
        .build()
    );
private long lastBlockPlaceTime = 0;
private int blocksPlacedThisSecond = 0;
private long blockPlaceSecond = 0;
    private final SettingGroup sgRender = settings.createGroup("Visual Rendering");
    // === CITY MINING SETTINGS ===
    private final SettingGroup sgCityMining = settings.createGroup("City Mining");
   // === ENUMS ===

   public enum RotationMode {
       None("None"),
       Break("Break Only"),
       Place("Place Only"),
       Both("Both");

       private final String name;
       RotationMode(String name) { this.name = name; }
       @Override public String toString() { return name; }
   }

   public enum SwitchMode {
       Disabled("Disabled"),
       Normal("Normal"),
       Silent("Silent"),
       Instant("Instant");

       private final String name;
       SwitchMode(String name) { this.name = name; }
       @Override public String toString() { return name; }
   }

   public enum SupportMode {
       Disabled("Disabled"),
       Accurate("Accurate"),
       Fast("Fast"),
       Smart("Smart");

       private final String name;
       SupportMode(String name) { this.name = name; }
       @Override public String toString() { return name; }
   }

   public enum MultiCrystalMode {
       Disabled("Disabled"),
       Dual("Dual Crystal"),
       Triple("Triple Crystal"),
       Quad("Quad Crystal"),
       Penta("Penta Crystal"),
       Adaptive("Adaptive"),
       Burst("Burst Mode");

       private final String name;
       MultiCrystalMode(String name) { this.name = name; }
       @Override public String toString() { return name; }
   }

   public enum PlacementPattern {
       Surrounding("Surrounding"),
       Linear("Linear"),
       Cross("Cross Pattern"),
       Diamond("Diamond"),
       Optimal("Optimal Damage"),
       Trap("Trap Formation");

       private final String name;
       PlacementPattern(String name) { this.name = name; }
       @Override public String toString() { return name; }
   }

   // New: Escape method preference enum
   public enum EscapeMethod {
       Chorus("Chorus Fruit Only"),
       Pearl("Ender Pearl Only"),
       Both("Chorus Fruit + Ender Pearl");

       private final String name;
       EscapeMethod(String name) { this.name = name; }
       @Override public String toString() { return name; }
   }

   public enum AutoPearlMode {
       Disabled("Disabled"),
       Health("Health Based"),
       Totems("Totem Based"),
       Combined("Health + Totems"),
       Smart("Smart Escape");

       private final String name;
       AutoPearlMode(String name) { this.name = name; }
       @Override public String toString() { return name; }
   }

   public enum TargetPriority {
       Closest("Closest"),
       LowestHealth("Lowest Health"),
       HighestDamage("Highest Damage"),
       MostArmor("Most Armor"),
       LeastArmor("Least Armor");

       private final String name;
       TargetPriority(String name) { this.name = name; }
       @Override public String toString() { return name; }
   }

    // === ARMOR MENDING SETTINGS ===
    private final SettingGroup sgMending = settings.createGroup("Armor Mending");

    private final Setting<Double> helmetDurability = sgMending.add(new DoubleSetting.Builder()
        .name("helmet-durability-threshold")
        .description("Minimum helmet durability percentage before mending.")
        .defaultValue(20.0)
        .min(0)
        .max(100)
        .sliderMax(100)
        .build()
    );
    private final Setting<Double> chestDurability = sgMending.add(new DoubleSetting.Builder()
        .name("chestplate-durability-threshold")
        .description("Minimum chestplate durability percentage before mending.")
        .defaultValue(20.0)
        .min(0)
        .max(100)
        .sliderMax(100)
        .build()
    );
    private final Setting<Double> leggingsDurability = sgMending.add(new DoubleSetting.Builder()
        .name("leggings-durability-threshold")
        .description("Minimum leggings durability percentage before mending.")
        .defaultValue(20.0)
        .min(0)
        .max(100)
        .sliderMax(100)
        .build()
    );
    private final Setting<Double> bootsDurability = sgMending.add(new DoubleSetting.Builder()
        .name("boots-durability-threshold")
        .description("Minimum boots durability percentage before mending.")
        .defaultValue(20.0)
        .min(0)
        .max(100)
        .sliderMax(100)
        .build()
    );
    private final Setting<Boolean> pauseWhileMending = sgMending.add(new BoolSetting.Builder()
        .name("pause-while-mending")
        .description("Pause all non-safety actions while mending armor with XP bottles.")
        .defaultValue(true)
        .build()
    );

    // === GENERAL SETTINGS ===
    private final Setting<Boolean> demonMode = sgGeneral.add(new BoolSetting.Builder()
        .name("demon-mode")
        .description("Unleash the full demonic power - enables all aggressive features and optimizations.")
        .defaultValue(true)
        .build()
    );

    // === HYPER-AGGRESSIVE MODE ===
    private final Setting<Boolean> hyperAggressiveMode = sgGeneral.add(new BoolSetting.Builder()
        .name("hyper-aggressive-mode")
        .description("Overrides all relevant settings for the fastest, highest-damage crystal placement and breaking possible.")
        .defaultValue(false)
        .build()
    );

    private final Setting<Boolean> pauseOnEat = sgGeneral.add(new BoolSetting.Builder()
        .name("pause-on-eat")
        .description("Pause crystal combat while eating gapples or other food items.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> pauseOnMine = sgGeneral.add(new BoolSetting.Builder()
        .name("pause-on-mine")
        .description("Pause crystal combat while mining blocks.")
        .defaultValue(true)
        .build()
    );

    private final Setting<RotationMode> rotationMode = sgGeneral.add(new EnumSetting.Builder<RotationMode>()
        .name("rotation-mode")
        .description("When to rotate towards crystals for server-side legitimacy.")
        .defaultValue(RotationMode.Both)
        .build()
    );

    private final Setting<Double> rotationSpeed = sgGeneral.add(new DoubleSetting.Builder()
        .name("rotation-speed")
        .description("Maximum degrees to rotate per tick - lower values look more legit.")
        .defaultValue(180)
        .range(1, 350)
        .sliderMax(180)
        .build()
    );

    private final Setting<Boolean> autoGap = sgGeneral.add(new BoolSetting.Builder()
        .name("auto-gap")
        .description("Automatically eat a golden apple when health is low during combat.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> gapHealthThreshold = sgGeneral.add(new IntSetting.Builder()
        .name("gap-health-threshold")
        .description("Health threshold (including absorption) to eat a golden apple.")
        .defaultValue(16)
        .min(1)
        .sliderMax(40)
        .build()
    );

    // === DEMONIC TARGETING ===
    private final Setting<Double> targetRange = sgTargeting.add(new DoubleSetting.Builder()
        .name("target-range")
        .description("Maximum range to search for enemies to obliterate with crystals.")
        .defaultValue(12)
        .min(1)
        .sliderMax(20)
        .build()
    );

    private final Setting<TargetPriority> targetPriority = sgTargeting.add(new EnumSetting.Builder<TargetPriority>()
        .name("target-priority")
        .description("How to prioritize multiple targets for maximum demonic efficiency.")
        .defaultValue(TargetPriority.LowestHealth)
        .build()
    );

    private final Setting<Boolean> predictMovement = sgTargeting.add(new BoolSetting.Builder()
        .name("predict-movement")
        .description("Predict enemy movement to place crystals where they will be, not where they are.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> predictionTicks = sgTargeting.add(new DoubleSetting.Builder()
        .name("prediction-ticks")
        .description("How many ticks ahead to predict enemy movement.")
        .defaultValue(3)
        .min(1)
        .max(60)
        .sliderMax(10)
        .visible(predictMovement::get)
        .build()
    );

    private final Setting<Boolean> ignoreNaked = sgTargeting.add(new BoolSetting.Builder()
        .name("ignore-naked")
        .description("Ignore players with no armor - they're not worth Your crystals.")
        .defaultValue(false)
        .build()
    );

    private final Setting<Set<EntityType<?>>> targetEntities = sgTargeting.add(new EntityTypeListSetting.Builder()
        .name("target-entities")
        .description("Types of entities to target with demonic crystal fury.")
        .onlyAttackable()
        .defaultValue(EntityType.PLAYER, EntityType.WARDEN, EntityType.WITHER)
        .build()
    );

    private final Setting<Boolean> smartTargeting = sgTargeting.add(new BoolSetting.Builder()
        .name("smart-targeting")
        .description("Use advanced targeting algorithms considering multiple factors.RvD//AvD//others")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> targetSwitching = sgTargeting.add(new BoolSetting.Builder()
        .name("target-switching")
        .description("Automatically switch targets based on optimal Rage damage output.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> switchThreshold = sgTargeting.add(new DoubleSetting.Builder()
        .name("switch-threshold")
        .description("Damage difference required to switch targets (higher = less switching).")
        .defaultValue(4.0)
        .min(0)
        .sliderMax(20)
        .visible(targetSwitching::get)
        .build()
    );

    private final Setting<Boolean> predictiveTargeting = sgTargeting.add(new BoolSetting.Builder()
        .name("predictive-targeting")
        .description("Target where enemies will be, not where they are now.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> predictionAccuracy = sgTargeting.add(new DoubleSetting.Builder()
        .name("prediction-accuracy")
        .description("How accurate movement prediction should be (higher = more accurate but slower).")
        .defaultValue(0.8)
        .min(0.1)
        .max(1.0)
        .sliderMax(1.0)
        .visible(predictiveTargeting::get)
        .build()
    );

    // === CRYSTAL PLACEMENT ===
    private final Setting<Boolean> enablePlacement = sgPlacement.add(new BoolSetting.Builder()
        .name("enable-placement")
        .description("Enable crystal placement - why are you here if you don't want to place crystals?")
        .defaultValue(true)
        .build()
    );

    /**
     * Number of blocks to place per action (user-defined, 1-4).
     * Each placement is validated before proceeding to the next.
     */
    private final Setting<Integer> blocksPerAction = sgPlacement.add(new IntSetting.Builder()
        .name("blocks-per-action")
        .description("Number of blocks to place per action (1-4). Each placement is validated before the next.")
        .defaultValue(1)
        .min(1)
        .max(4)
        .sliderMax(4)
        .build()
    );

    private final Setting<Double> placeRange = sgPlacement.add(new DoubleSetting.Builder()
        .name("place-range")
        .description("Maximum range to place crystals for optimal battlefield control.")
        .defaultValue(5.0)
        .min(1)
        .sliderMax(6)
        .build()
    );

    private final Setting<Double> wallsRange = sgPlacement.add(new DoubleSetting.Builder()
        .name("walls-range")
        .description("Range to place crystals when obstructed by walls or obstacles.")
        .defaultValue(3.5)
        .min(1)
        .sliderMax(6)
        .build()
    );

    private final Setting<Double> minDamagePlace = sgPlacement.add(new DoubleSetting.Builder()
        .name("min-damage-place")
        .description("Minimum damage a crystal must deal to enemies to be worth placing. ")
        .defaultValue(6.0)
        .min(0)
        .sliderMax(36)
        .build()
    );

    private final Setting<Boolean> placement112 = sgPlacement.add(new BoolSetting.Builder()
        .name("1.12-placement")
        .description("Use 1.12 crystal placement rules for maximum compatibility. with old servers")
        .defaultValue(false)
        .build()
    );

    private final Setting<SupportMode> supportMode = sgPlacement.add(new EnumSetting.Builder<SupportMode>()
        .name("support-mode")
        .description("Automatically place support blocks when no valid crystal positions exist.")
        .defaultValue(SupportMode.Smart)
        .build()
    );

    private final Setting<Boolean> facePlaceMode = sgPlacement.add(new BoolSetting.Builder()
        .name("face-place")
        .description("Enable face-place mode for low-health enemies - show no mercy.")
        .defaultValue(true)
        .build()
    );

    // User-configurable valid base blocks for crystal placement
    private final Setting<List<Block>> validCrystalBaseBlocks = sgPlacement.add(new BlockListSetting.Builder()
        .name("valid-crystal-base-blocks")
        .description("Blocks allowed as valid bases for crystal placement (Obsidian, Bedrock, etc).")
        .defaultValue(List.of(Blocks.OBSIDIAN, Blocks.BEDROCK))
        .build()
    );

    private final Setting<Double> facePlaceHealth = sgPlacement.add(new DoubleSetting.Builder()
        .name("face-place-health")
        .description("Enemy health threshold to trigger face-place mode.")
        .defaultValue(8.0)
        .min(1)
        .sliderMax(36)
        .build()
    );

    // === MULTI-CRYSTAL WARFARE ===
    private final Setting<MultiCrystalMode> multiCrystalMode = sgMultiCrystal.add(new EnumSetting.Builder<MultiCrystalMode>()
        .name("multi-crystal-mode")
        .description("Deploy multiple crystals simultaneously for overwhelming demonic assault.")
        .defaultValue(MultiCrystalMode.Dual)
        .build()
    );

    private final Setting<PlacementPattern> placementPattern = sgMultiCrystal.add(new EnumSetting.Builder<PlacementPattern>()
        .name("placement-pattern")
        .description("Pattern for multi-crystal placement to maximize damage and control.")
        .defaultValue(PlacementPattern.Optimal)
        .build()
    );

    private final Setting<Integer> maxCrystals = sgMultiCrystal.add(new IntSetting.Builder()
        .name("max-crystals")
        .description("Maximum number of crystals to place simultaneously (1-5).")
        .defaultValue(3)
        .min(1)
        .max(5)
        .sliderMax(5)
        .build()
    );

    private final Setting<Double> multiCrystalRange = sgMultiCrystal.add(new DoubleSetting.Builder()
        .name("multi-range")
        .description("Maximum distance between multiple crystal placements.")
        .defaultValue(3.0)
        .min(1)
        .sliderMax(8)
        .build()
    );

    private final Setting<Integer> multiCrystalDelay = sgMultiCrystal.add(new IntSetting.Builder()
        .name("multi-delay")
        .description("Delay in ticks between placing multiple crystals.")
        .defaultValue(1)
        .min(0)
        .sliderMax(10)
        .build()
    );

    private final Setting<Boolean> multiCrystalSync = sgMultiCrystal.add(new BoolSetting.Builder()
        .name("synchronized-detonation")
        .description("Synchronize detonation of multiple crystals for maximum devastation.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> multiCrystalMinDamage = sgMultiCrystal.add(new DoubleSetting.Builder()
        .name("multi-min-damage")
        .description("Minimum combined damage from all crystals to justify multi-placement.")
        .defaultValue(12.0)
        .min(0)
        .sliderMax(50)
        .build()
    );

    private final Setting<Boolean> intelligentSpacing = sgMultiCrystal.add(new BoolSetting.Builder()
        .name("intelligent-spacing")
        .description("Automatically calculate optimal spacing between crystals for maximum damage.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> avoidOverlap = sgMultiCrystal.add(new BoolSetting.Builder()
        .name("avoid-overlap")
        .description("Prevent crystal damage overlap to maximize total damage output.")
        .defaultValue(true)
        .build()
    );

    // === TARGET TRAPPING ===
    private final Setting<Boolean> enableTrapping = sgTrapping.add(new BoolSetting.Builder()
        .name("enable-trapping")
        .description("Place blocks near and above targets to trap them for maximum crystal damage.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> trapAbove = sgTrapping.add(new BoolSetting.Builder()
        .name("trap-above")
        .description("Place blocks above the target to prevent vertical escape.")
        .defaultValue(true)
        .visible(enableTrapping::get)
        .build()
    );

    private final Setting<Boolean> trapSides = sgTrapping.add(new BoolSetting.Builder()
        .name("trap-sides")
        .description("Place blocks around the target to prevent horizontal escape.")
        .defaultValue(true)
        .visible(enableTrapping::get)
        .build()
    );

    private final Setting<Double> trapRange = sgTrapping.add(new DoubleSetting.Builder()
        .name("trap-range")
        .description("Maximum range to place trap blocks from the target.")
        .defaultValue(4.0)
        .min(1)
        .sliderMax(6)
        .visible(enableTrapping::get)
        .build()
    );

    private final Setting<Integer> maxTrapBlocks = sgTrapping.add(new IntSetting.Builder()
        .name("max-trap-blocks")
        .description("Maximum number of trap blocks to place per target.")
        .defaultValue(6)
        .min(1)
        .sliderMax(12)
        .visible(enableTrapping::get)
        .build()
    );

    private final Setting<Boolean> smartTrapping = sgTrapping.add(new BoolSetting.Builder()
        .name("smart-trapping")
        .description("Intelligently place trap blocks based on target movement and escape routes.")
        .defaultValue(true)
        .visible(enableTrapping::get)
        .build()
    );

    private final Setting<Boolean> trapOnlyLowHealth = sgTrapping.add(new BoolSetting.Builder()
        .name("trap-only-low-health")
        .description("Only trap targets when they have low health for finishing moves.")
        .defaultValue(false)
        .visible(enableTrapping::get)
        .build()
    );

    private final Setting<Double> trapHealthThreshold = sgTrapping.add(new DoubleSetting.Builder()
        .name("trap-health-threshold")
        .description("Health threshold below which to start trapping targets.")
        .defaultValue(10.0)
        .min(1)
        .sliderMax(20)
        .visible(() -> enableTrapping.get() && trapOnlyLowHealth.get())
        .build()
    );

   // === AUTO-PEARL ESCAPE ===
   // New: Escape method preference setting
   private final Setting<EscapeMethod> escapeMethod = sgAutoPearl.add(new EnumSetting.Builder<EscapeMethod>()
       .name("escape-method")
       .description("Preferred escape method: chorus fruit, ender pearl, or both. If both, chorus fruit is prioritized if available.")
       .defaultValue(EscapeMethod.Both)
       .build()
   );

   private final Setting<AutoPearlMode> autoPearlMode = sgAutoPearl.add(new EnumSetting.Builder<AutoPearlMode>()
       .name("auto-pearl-mode")
       .description("Automatically escape using configured method when death Comes knocking.")
       .defaultValue(AutoPearlMode.Combined)
       .build()
   );

   private final Setting<Double> pearlHealthThreshold = sgAutoPearl.add(new DoubleSetting.Builder()
       .name("health-threshold")
       .description("Health level (including absorption) to trigger emergency escape. set higher for more aggressive escapes.")
       .defaultValue(6.0)
       .min(1)
       .sliderMax(20)
       .build()
   );

   private final Setting<Integer> pearlTotemThreshold = sgAutoPearl.add(new IntSetting.Builder()
       .name("totem-threshold")
       .description("Number of totems remaining to trigger emergency escape. are you affraid of death?")
       .defaultValue(1)
       .min(0)
       .sliderMax(5)
       .build()
   );

   private final Setting<Double> pearlDistance = sgAutoPearl.add(new DoubleSetting.Builder()
       .name("pearl-distance")
       .description("Minimum distance to throw pearls for effective escape. you must be looking where you want throw them. no hand holding.")
       .defaultValue(15.0)
       .min(5)
       .sliderMax(30)
       .build()
   );

   private final Setting<Boolean> pearlOnlyWhenTargeted = sgAutoPearl.add(new BoolSetting.Builder()
       .name("pearl-only-when-targeted")
       .description("Only use emergency escape when actively being targeted by enemies. -auto flinch-")
       .defaultValue(true)
       .build()
   );

   private final Setting<Integer> pearlCooldown = sgAutoPearl.add(new IntSetting.Builder()
       .name("pearl-cooldown")
       .description("Cooldown in ticks between escape attempts to prevent spam. or get around dumb anti-cheat.")
       .defaultValue(40)
       .min(20)
       .sliderMax(100)
       .build()
   );

    // === CRYSTAL BREAKING ===
    private final Setting<Boolean> enableBreaking = sgBreaking.add(new BoolSetting.Builder()
        .name("enable-breaking")
        .description("Enable crystal breaking - destroy enemy crystals and detonate your own.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> breakRange = sgBreaking.add(new DoubleSetting.Builder()
        .name("break-range")
        .description("Maximum range to break crystals with demonic precision.")
        .defaultValue(5.0)
        .min(1)
        .sliderMax(6)
        .build()
    );

    private final Setting<Double> minDamageBreak = sgBreaking.add(new DoubleSetting.Builder()
        .name("min-damage-break")
        .description("Minimum damage to enemies required to break a crystal.")
        .defaultValue(4.0)
        .min(0)
        .sliderMax(36)
        .build()
    );

    private final Setting<Boolean> inhibit = sgBreaking.add(new BoolSetting.Builder()
        .name("inhibit")
        .description("Prevent enemies from placing crystals by breaking them instantly.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> breakAttempts = sgBreaking.add(new IntSetting.Builder()
        .name("break-attempts")
        .description("Maximum attempts to break a crystal before giving up.")
        .defaultValue(3)
        .min(1)
        .sliderMax(10)
        .build()
    );

    private final Setting<Boolean> burstBreaking = sgBreaking.add(new BoolSetting.Builder()
        .name("burst-breaking")
        .description("Enable burst breaking for maximum crystal destruction speed.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> burstCount = sgBreaking.add(new IntSetting.Builder()
        .name("burst-count")
        .description("Number of crystals to break in a single burst.")
        .defaultValue(3)
        .min(1)
        .max(10)
        .sliderMax(10)
        .visible(burstBreaking::get)
        .build()
    );

    private final Setting<Integer> burstDelay = sgBreaking.add(new IntSetting.Builder()
        .name("burst-delay")
        .description("Delay in milliseconds between burst attacks.")
        .defaultValue(50)
        .min(0)
        .sliderMax(200)
        .visible(burstBreaking::get)
        .build()
    );

    private final Setting<Boolean> packetBreaking = sgBreaking.add(new BoolSetting.Builder()
        .name("packet-breaking")
        .description("Use packet-level breaking for maximum speed and server-side legitimacy.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> instantBreak = sgBreaking.add(new BoolSetting.Builder()
        .name("instant-break")
        .description("Attempt to break crystals instantly upon placement detection.")
        .defaultValue(true)
        .build()
    );

    // === ITEM SWITCHING ===
    private final Setting<SwitchMode> switchMode = sgSwitching.add(new EnumSetting.Builder<SwitchMode>()
        .name("switch-mode")
        .description("How to switch to crystals and tools automatically.")
        .defaultValue(SwitchMode.Normal)
        .build()
    );

    private final Setting<Boolean> antiWeakness = sgSwitching.add(new BoolSetting.Builder()
        .name("anti-weakness")
        .description("Switch to tools when affected by weakness to break crystals effectively.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> noGapSwitch = sgSwitching.add(new BoolSetting.Builder()
        .name("no-gap-switch")
        .description("Don't switch items while holding gapples - prioritize healing.")
        .defaultValue(true)
        .build()
    );

    // === SAFETY SYSTEMS ===
    private final Setting<Boolean> antiSuicide = sgSafety.add(new BoolSetting.Builder()
        .name("anti-suicide")
        .description("Prevent placing/breaking crystals that would kill you - self-preservation.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> maxSelfDamage = sgSafety.add(new DoubleSetting.Builder()
        .name("max-self-damage")
        .description("Maximum damage crystals can deal to yourself before being considered unsafe.")
        .defaultValue(8.0)
        .min(0)
        .sliderMax(36)
        .build()
    );

    private final Setting<Boolean> safetyCheck = sgSafety.add(new BoolSetting.Builder()
        .name("safety-check")
        .description("Perform additional safety checks before crystal operations.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> emergencyDisable = sgSafety.add(new BoolSetting.Builder()
        .name("emergency-disable")
        .description("Automatically disable module in dangerous situations.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> emergencyHealthThreshold = sgSafety.add(new DoubleSetting.Builder()
        .name("emergency-health")
        .description("Health level to trigger emergency disable.")
        .defaultValue(4.0)
        .min(1)
        .sliderMax(20)
        .visible(emergencyDisable::get)
        .build()
    );

    private final Setting<Boolean> totemSafety = sgSafety.add(new BoolSetting.Builder()
        .name("totem-safety")
        .description("Enhanced safety when holding totems.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> minTotems = sgSafety.add(new IntSetting.Builder()
        .name("min-totems")
        .description("Minimum totems required for aggressive crystal combat.")
        .defaultValue(2)
        .min(0)
        .sliderMax(10)
        .visible(totemSafety::get)
        .build()
    );

    private final Setting<Boolean> armorSafety = sgSafety.add(new BoolSetting.Builder()
        .name("armor-safety")
        .description("Adjust aggression based on armor durability.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> minArmorDurability = sgSafety.add(new DoubleSetting.Builder()
        .name("min-armor-durability")
        .description("Minimum armor durability percentage for safe operation.")
        .defaultValue(20.0)
        .min(0)
        .max(100)
        .sliderMax(100)
        .visible(armorSafety::get)
        .build()
    );

    private final Setting<Boolean> holeSafety = sgSafety.add(new BoolSetting.Builder()
        .name("hole-safety")
        .description("Only perform aggressive actions when in a safe hole.")
        .defaultValue(false)
        .build()
    );

    /**
     * Enable/disable surround self-protection, block type, and radius.
     */
    private final Setting<Boolean> surroundCheck = sgSafety.add(new BoolSetting.Builder()
        .name("surround-check")
        .description("Enable surround self-protection logic before crystal operations.")
        .defaultValue(true)
        .visible(holeSafety::get)
        .build()
    );
    /**
     * Block type to use for surround/self-protection.
     */
    private final Setting<List<Item>> surroundBlockTypes = sgSafety.add(new ItemListSetting.Builder()
        .name("surround-block-types")
        .description("Block types to use for surround/self-protection (e.g., obsidian, ender chest, etc).")
        .defaultValue(List.of(Items.OBSIDIAN, Items.CRYING_OBSIDIAN, Items.NETHERITE_BLOCK, Items.ENDER_CHEST, Items.RESPAWN_ANCHOR))
        .build()
    );
    /**
     * Radius (distance) for surround/self-protection blocks.
     */
    private final Setting<Integer> surroundRadius = sgSafety.add(new IntSetting.Builder()
        .name("surround-radius")
        .description("Radius (distance) for surround/self-protection blocks (1 = classic surround, 2 = expanded).")
        .defaultValue(1)
        .min(1)
        .max(3)
        .sliderMax(3)
        .build()
    );

    // === TIMING & DELAYS ===
    private final Setting<Integer> placeDelay = sgTiming.add(new IntSetting.Builder()
        .name("place-delay")
        .description("Delay in ticks between crystal placements for timing control.")
        .defaultValue(0)
        .min(0)
        .sliderMax(20)
        .build()
    );

    private final Setting<Integer> breakDelay = sgTiming.add(new IntSetting.Builder()
        .name("break-delay")
        .description("Delay in ticks between crystal breaks for optimal timing.")
        .defaultValue(0)
        .min(0)
        .sliderMax(20)
        .build()
    );

    private final Setting<Integer> switchDelay = sgTiming.add(new IntSetting.Builder()
        .name("switch-delay")
        .description("Delay in ticks after switching items before performing actions.")
        .defaultValue(0)
        .min(0)
        .sliderMax(10)
        .build()
    );

    private final Setting<Boolean> speedMode = sgTiming.add(new BoolSetting.Builder()
        .name("speed-mode")
        .description("Enable maximum speed optimizations for crystal combat.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> noDelayMode = sgTiming.add(new BoolSetting.Builder()
        .name("no-delay-mode")
        .description("Remove all delays for maximum speed (may be less stable).")
        .defaultValue(false)
        .visible(speedMode::get)
        .build()
    );

    private final Setting<Integer> tickOptimization = sgTiming.add(new IntSetting.Builder()
        .name("tick-optimization")
        .description("Number of actions to perform per tick (higher = faster but less stable).")
        .defaultValue(2)
        .min(1)
        .max(10)
        .sliderMax(10)
        .visible(speedMode::get)
        .build()
    );

    private final Setting<Boolean> packetOptimization = sgTiming.add(new BoolSetting.Builder()
        .name("packet-optimization")
        .description("Optimize packet timing for maximum speed and server compatibility.")
        .defaultValue(true)
        .visible(speedMode::get)
        .build()
    );

    private final Setting<Integer> packetDelay = sgTiming.add(new IntSetting.Builder()
        .name("packet-delay")
        .description("Minimum delay between packets in milliseconds.")
        .defaultValue(25)
        .min(0)
        .sliderMax(100)
        .visible(() -> speedMode.get() && packetOptimization.get())
        .build()
    );

    // === VISUAL RENDERING ===
    private final Setting<Boolean> renderPlacement = sgRender.add(new BoolSetting.Builder()
        .name("render-placement")
        .description("Render placement positions for crystal targeting visualization.")
        .defaultValue(true)
        .build()
    );

    // === Enhanced Rendering Controls ===
    private final Setting<Boolean> crystalVisible = sgRender.add(new BoolSetting.Builder()
        .name("crystal-visible")
        .description("Show End Crystal entities.")
        .defaultValue(true)
        .build()
    );
    private final Setting<Boolean> explosionVisible = sgRender.add(new BoolSetting.Builder()
        .name("explosion-visible")
        .description("Show explosion effects.")
        .defaultValue(true)
        .build()
    );
    private final Setting<Double> crystalSize = sgRender.add(new DoubleSetting.Builder()
        .name("crystal-size")
        .description("Scale factor for End Crystal rendering.")
        .defaultValue(1.0)
        .min(0.1)
        .max(5.0)
        .sliderMax(3.0)
        .build()
    );
    private final Setting<Double> explosionSize = sgRender.add(new DoubleSetting.Builder()
        .name("explosion-size")
        .description("Scale factor for explosion rendering.")
        .defaultValue(1.0)
        .min(0.1)
        .max(5.0)
        .sliderMax(3.0)
        .build()
    );
    private final Setting<SettingColor> explosionColor = sgRender.add(new ColorSetting.Builder()
        .name("explosion-color")
        .description("Color for explosion rendering.")
        .defaultValue(new SettingColor(255, 128, 0, 120))
        .build()
    );
    public enum DynamicEffectMode {
        None("None"),
        Pulse("Pulse"),
        Rainbow("Rainbow"),
        Wave("Wave");
        private final String name;
        DynamicEffectMode(String name) { this.name = name; }
        @Override public String toString() { return name; }
    }
    private final Setting<DynamicEffectMode> crystalDynamicEffect = sgRender.add(new EnumSetting.Builder<DynamicEffectMode>()
        .name("crystal-dynamic-effect")
        .description("Dynamic visual effect for End Crystals.")
        .defaultValue(DynamicEffectMode.None)
        .build()
    );
    private final Setting<DynamicEffectMode> explosionDynamicEffect = sgRender.add(new EnumSetting.Builder<DynamicEffectMode>()
        .name("explosion-dynamic-effect")
        .description("Dynamic visual effect for explosions.")
        .defaultValue(DynamicEffectMode.None)
        .build()
    );

    private final Setting<SettingColor> placementColor = sgRender.add(new ColorSetting.Builder()
        .name("placement-color")
        .description("Color for crystal placement position rendering.")
        .defaultValue(new SettingColor(255, 0, 0, 100))
        .build()
    );

    private final Setting<SettingColor> breakingColor = sgRender.add(new ColorSetting.Builder()
        .name("breaking-color")
        .description("Color for crystal breaking target rendering.")
        .defaultValue(new SettingColor(255, 255, 0, 100))
        .build()
    );

    private final Setting<ShapeMode> shapeMode = sgRender.add(new EnumSetting.Builder<ShapeMode>()
        .name("shape-mode")
        .description("Rendering mode for crystal position visualization.")
        .defaultValue(ShapeMode.Both)
        .build()
    );

    private final Setting<Boolean> renderTarget = sgRender.add(new BoolSetting.Builder()
        .name("render-target")
        .description("Render current target with highlighting.")
        .defaultValue(true)
        .build()
    );

    private final Setting<SettingColor> targetColor = sgRender.add(new ColorSetting.Builder()
        .name("target-color")
        .description("Color for target highlighting.")
        .defaultValue(new SettingColor(255, 100, 100, 150))
        .visible(renderTarget::get)
        .build()
    );

    private final Setting<Boolean> renderPrediction = sgRender.add(new BoolSetting.Builder()
        .name("render-prediction")
        .description("Render predicted target movement path.")
        .defaultValue(true)
        .build()
    );

    private final Setting<SettingColor> predictionColor = sgRender.add(new ColorSetting.Builder()
        .name("prediction-color")
        .description("Color for movement prediction visualization.")
        .defaultValue(new SettingColor(100, 255, 100, 100))
        .visible(renderPrediction::get)
        .build()
    );

    private final Setting<Boolean> renderDamage = sgRender.add(new BoolSetting.Builder()
        .name("render-damage")
        .description("Render damage numbers for crystal positions.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> renderTrapBlocks = sgRender.add(new BoolSetting.Builder()
        .name("render-trap-blocks")
        .description("Render trap block positions.")
        .defaultValue(true)
        .build()
    );

    private final Setting<SettingColor> trapColor = sgRender.add(new ColorSetting.Builder()
        .name("trap-color")
        .description("Color for trap block visualization.")
        .defaultValue(new SettingColor(255, 165, 0, 100))
        .visible(renderTrapBlocks::get)
        .build()
    );

    private final Setting<Boolean> debugMode = sgRender.add(new BoolSetting.Builder()
        .name("debug-mode")
        .description("Enable debug information display.")
        .defaultValue(false)
        .build()
    );

    private final Setting<Boolean> renderDebugInfo = sgRender.add(new BoolSetting.Builder()
        .name("render-debug-info")
        .description("Render debug information on screen.")
        .defaultValue(true)
        .visible(debugMode::get)
        .build()
    );

    // === FIELDS ===
    private final List<BlockPos> multiCrystalPositions = new ArrayList<>();
    private final List<BlockPos> trapBlockPositions = new ArrayList<>();
    private final List<EndCrystalEntity> targetCrystals = new ArrayList<>();
    private final List<EndCrystalEntity> burstTargets = new ArrayList<>();
    private final Map<BlockPos, Double> positionDamageMap = new HashMap<>();
    private final Map<BlockPos, Integer> placementAttempts = new HashMap<>();
    private final Map<Integer, Integer> crystalBreakAttempts = new HashMap<>();
    private final Map<Integer, Long> crystalLastAttack = new HashMap<>();
    private final List<Entity> potentialTargets = new ArrayList<>();

    // City Mining state
    private boolean cityMiningActive = false;
    private PlayerEntity cityMiningTarget = null;
    private BlockPos cityMiningBlock = null;
    private FindItemResult cityMiningPick = null;
    private float cityMiningProgress = 0.0f;

    // === CITY MINING USER SETTINGS ===
    private final Setting<Boolean> cityMiningSupport = sgCityMining.add(new BoolSetting.Builder()
        .name("support-block")
        .description("If there is no block below a city block, place one before mining.")
        .defaultValue(true)
        .build()
    );
    private final Setting<Boolean> cityMiningSwingHand = sgCityMining.add(new BoolSetting.Builder()
        .name("swing-hand")
        .description("Whether to render your hand swinging during city mining.")
        .defaultValue(false)
        .build()
    );
    private final Setting<Boolean> cityMiningRenderBlock = sgCityMining.add(new BoolSetting.Builder()
        .name("render-block")
        .description("Whether to render the block being mined during city mining.")
        .defaultValue(true)
        .build()
    );

// === BLOCK PLACEMENT BPS THROTTLE ===
private boolean canPlaceBlockBPS() {
    long now = System.currentTimeMillis();
    long currentSecond = now / 1000;
    if (currentSecond != blockPlaceSecond) {
        blockPlaceSecond = currentSecond;
        blocksPlacedThisSecond = 0;
    }
    if (blocksPlacedThisSecond < blocksPerSecond.get()) {
        blocksPlacedThisSecond++;
        lastBlockPlaceTime = now;
        return true;
    }
    return false;
}
    private final Setting<ShapeMode> cityMiningShapeMode = sgCityMining.add(new EnumSetting.Builder<ShapeMode>()
        .name("shape-mode")
        .description("How the city mining block shapes are rendered.")
        .defaultValue(ShapeMode.Both)
        .visible(cityMiningRenderBlock::get)
        .build()
    );
    private final Setting<SettingColor> cityMiningSideColor = sgCityMining.add(new ColorSetting.Builder()
        .name("side-color")
        .description("The side color of the city mining rendering.")
        .defaultValue(new SettingColor(225, 0, 0, 75))
        .visible(() -> cityMiningRenderBlock.get() && cityMiningShapeMode.get().sides())
        .build()
    );
    private final Setting<SettingColor> cityMiningLineColor = sgCityMining.add(new ColorSetting.Builder()
        .name("line-color")
        .description("The line color of the city mining rendering.")
        .defaultValue(new SettingColor(225, 0, 0, 255))
        .visible(() -> cityMiningRenderBlock.get() && cityMiningShapeMode.get().lines())
        .build()
    );
    // Additional city mining configurability
    private final Setting<Double> cityMiningRange = sgCityMining.add(new DoubleSetting.Builder()
        .name("city-mining-range")
        .description("Maximum range to search for city mining targets.")
        .defaultValue(5.0)
        .min(1)
        .sliderMax(12)
        .build()
    );
    private final Setting<List<Item>> cityMiningPickaxeTypes = sgCityMining.add(new ItemListSetting.Builder()
        .name("pickaxe-types")
        .description("Pickaxe types to use for city mining.")
        .defaultValue(List.of(Items.DIAMOND_PICKAXE, Items.NETHERITE_PICKAXE))
        .build()
    );
    private final Setting<Integer> cityMiningDelay = sgCityMining.add(new IntSetting.Builder()
        .name("mining-delay")
        .description("Delay in ticks between mining actions during city mining.")
        .defaultValue(0)
        .min(0)
        .sliderMax(20)
        .build()
    );

    // AutoGap state variables
    private boolean gapEating = false;
    private int gapSlot = -1, gapPrevSlot = -1;
    private Entity currentTarget;
    private Entity lastTarget;
    private Vec3d predictedTargetPos;
    private int pearlCooldownTicks = 0;
    private int placeTicks = 0;
    private int breakTicks = 0;
    private int switchTicks = 0;
    private int burstPlaceCount = 0;
    private int burstBreakCount = 0;
    private boolean emergencyPearlUsed = false;
    private boolean isTrapping = false;
    private long lastPlacementTime = 0;
    private long lastBreakTime = 0;
    private long lastPacketTime = 0;
    private int actionsThisTick = 0;
    private int tickCounter = 0;

    // === DEFENSIVE: Enemy Crystal Placement Tracking ===
    // User-configurable pillar settings
    private final Setting<Integer> defensePillarHeight = sgTrapping.add(new IntSetting.Builder()
        .name("pillar-height")
        .description("Height of defensive pillars placed at enemy crystal positions.")
        .defaultValue(3)
        .min(1)
        .max(8)
        .sliderMax(8)
        .build()
    );
    private final Setting<List<Item>> defensePillarBlockTypes = sgTrapping.add(new ItemListSetting.Builder()
        .name("pillar-block-types")
        .description("Block types to use for defensive pillars (e.g., obsidian, cobblestone, etc).")
        .defaultValue(List.of(Items.OBSIDIAN, Items.COBBLESTONE, Items.STONE, Items.NETHERRACK))
        .build()
    );
    private static final int DEFENSE_PILLAR_QUEUE_SIZE = 8;
    private final Queue<BlockPos> enemyCrystalPositions = new ArrayDeque<>();
    private final Set<BlockPos> recentlyDefended = new HashSet<>();
    private final List<BlockPos> renderPillarPositions = new ArrayList<>();
    // Track pillar progress: base position -> current height
    private final Map<BlockPos, Integer> pillarProgress = new HashMap<>();
    // Debug: count pillar blocks placed this tick
    private int pillarBlocksPlacedThisTick = 0;

    public DemonCrystal() {
        super(Skylandia.Automation, "demon-crystal",
            "§4DemonCrystal§r - Advanced crystal automation with multi-crystal placement, intelligent targeting, " +
            "emergency escape (pearl/chorus), advanced safety (surround, self-protection), armor mending, " +
            "city mining (packet mining), and fully configurable automation and protection.");
        // Removed updateOverlayList() call (chams/overlay logic removed)
    }

    // === Overlay Management ===
    // Overlay/chams logic removed for performance and build stability.

    @Override
    public void onActivate() {
        info("§4DemonCrystal§r activated! Preparing for demonic crystal warfare...");
        // Removed updateOverlayList() call (chams/overlay logic removed)

        // Clear all collections
        multiCrystalPositions.clear();
        trapBlockPositions.clear();
        targetCrystals.clear();
        burstTargets.clear();
        positionDamageMap.clear();
        placementAttempts.clear();
        crystalBreakAttempts.clear();
        crystalLastAttack.clear();
        potentialTargets.clear();

        // Reset state variables
        currentTarget = null;
        lastTarget = null;
        predictedTargetPos = null;
        pearlCooldownTicks = 0;
        placeTicks = 0;
        breakTicks = 0;
        switchTicks = 0;
        burstPlaceCount = 0;
        burstBreakCount = 0;
        emergencyPearlUsed = false;
        isTrapping = false;
        lastPlacementTime = 0;
        lastBreakTime = 0;
        lastPacketTime = 0;
        actionsThisTick = 0;
        tickCounter = 0;

        // Reset city mining state
        cityMiningActive = false;
        cityMiningTarget = null;
        cityMiningBlock = null;
        cityMiningPick = null;
        cityMiningProgress = 0.0f;

        if (demonMode.get()) {
            info("§4DEMON MODE ACTIVATED§r - Unleashing maximum crystal fury!");
        }
    }

    @Override
    public void onDeactivate() {
        info("§4DemonCrystal§r deactivated. Crystal warfare ended. did you enjoy the carnage?");

        // Clear all collections
        multiCrystalPositions.clear();
        trapBlockPositions.clear();
        targetCrystals.clear();
        burstTargets.clear();
        positionDamageMap.clear();
        placementAttempts.clear();
        crystalBreakAttempts.clear();
        crystalLastAttack.clear();
        potentialTargets.clear();

        // Reset state
        currentTarget = null;
        lastTarget = null;
        predictedTargetPos = null;
        isTrapping = false;
    }

    // === EVENT HANDLERS ===
    /**
     * Tick handler refactored for instant batch reactions and always-on packet breaking.
     * - Batching/multi-crystal logic is retained.
     * - All batch actions (placement/breaking) are performed instantly per tick.
     * - Packet-level breaking is always enforced.
     * - No unnecessary delays between batch actions.
     * - Documentation and comments updated for clarity.
     */
    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (mc.player == null || mc.world == null) return;

        // Overlay/chams logic removed for performance and build stability.

        // --- Self-Protection: Place blocks around player before crystal actions ---
        placeSelfProtectionBlocks();

        // === AutoGap Integration ===
        if (autoGap.get()) {
            if (gapEating) {
                if (shouldGapEat()) {
                    if (!isGapItem(mc.player.getInventory().getStack(gapSlot))) {
                        int slot = findGapSlot();
                        if (slot == -1) {
                            stopGapEating();
                            return;
                        } else {
                            changeGapSlot(slot);
                        }
                    }
                    gapEat();
                } else {
                    stopGapEating();
                }
                return; // Pause all other actions while eating
            } else if (shouldGapEat()) {
                gapSlot = findGapSlot();
                if (gapSlot != -1) startGapEating();
                return; // Pause all other actions while eating
            }
        }

        // Reset per-tick counters
        actionsThisTick = 0;
        tickCounter++;

        // Update cooldowns with speed optimizations
        updateCooldowns();

        // Defensive: Build pillars at enemy crystal positions (does not block offense)
        handleDefensivePillars();

        // === City Mining Integration ===
        handleCityMining();

        // Check for emergency escape (chorus fruit or pearl)
        if (shouldUseEscape()) {
            useEscape();
            return;
        }

        // Pause checks
        if (shouldPause()) {
            return;
        }

        // Comprehensive safety checks
        if (!performSafetyChecks()) {
            return;
        }

        // Find target every tick for maximum reactivity
        findTarget();

        if (currentTarget == null) return;

        // Check if we have crystals for placement
        FindItemResult crystals = InvUtils.find(Items.END_CRYSTAL);
        if (!crystals.found() && enablePlacement.get()) return;

        // Instantly perform all batch crystal operations (placement and breaking)
        performInstantBatchCrystalOperations();
    }

    // --- Surround-style self-protection logic ---
    private void placeSelfProtectionBlocks() {
        // Only place if on ground and not already surrounded
        if (!mc.player.isOnGround()) return;

        // Only place blocks if no valid crystal placement is available
        FindItemResult crystals = InvUtils.find(Items.END_CRYSTAL);
        if (crystals.found()) return;

        // Use configured block types for surround/self-protection
        List<Item> blockTypes = surroundBlockTypes.get();
        FindItemResult blockItem = InvUtils.find(blockTypes.toArray(new Item[0]));
        if (!blockItem.found()) return;

        BlockPos playerPos = mc.player.getBlockPos();
        int radius = surroundRadius.get();
        List<BlockPos> surroundOffsets = new ArrayList<>();
        for (int dx = -radius; dx <= radius; dx++) {
            for (int dz = -radius; dz <= radius; dz++) {
                if (Math.abs(dx) + Math.abs(dz) == radius) {
                    surroundOffsets.add(playerPos.add(dx, 0, dz));
                }
            }
        }

        for (BlockPos pos : surroundOffsets) {
            if (!mc.world.getBlockState(pos).isAir()) continue;

            // Check for block below (for placement validity)
            if (mc.world.getBlockState(pos.down()).isAir()) continue;

            // Switch to block
            if (switchMode.get() != SwitchMode.Disabled) {
                InvUtils.swap(blockItem.slot(), switchMode.get() == SwitchMode.Silent);
            }

            // Rotate if needed
            if (rotationMode.get() == RotationMode.Place || rotationMode.get() == RotationMode.Both) {
                Vec3d blockVec = Vec3d.ofCenter(pos);
                Vec3d playerVec = mc.player.getEyePos();
                Vec3d direction = blockVec.subtract(playerVec).normalize();
                float yaw = (float) Math.toDegrees(Math.atan2(direction.z, direction.x)) - 90f;
                float pitch = (float) -Math.toDegrees(Math.asin(direction.y));
                Rotations.rotate(yaw, pitch);
            }

            // Place the block (BPS throttle)
            if (!canPlaceBlockBPS()) continue;
            BlockHitResult result = new BlockHitResult(
                Vec3d.ofCenter(pos.down()),
                Direction.UP,
                pos.down(),
                false
            );
            mc.interactionManager.interactBlock(mc.player, Hand.MAIN_HAND, result);

            // --- Crystal protection: break nearby crystals if block can't be placed ---
            if (mc.world.getBlockState(pos).isAir()) {
                Box box = new Box(
                    pos.getX() - 1, pos.getY() - 1, pos.getZ() - 1,
                    pos.getX() + 1, pos.getY() + 1, pos.getZ() + 1
                );
                for (Entity entity : mc.world.getOtherEntities(null, box)) {
                    if (entity instanceof EndCrystalEntity crystal) {
                        mc.player.networkHandler.sendPacket(PlayerInteractEntityC2SPacket.attack(crystal, mc.player.isSneaking()));
                        mc.getNetworkHandler().sendPacket(new HandSwingC2SPacket(Hand.MAIN_HAND));
                    }
                }
            }
        }
    }

    private void updateCooldowns() {
        if (isHyperAggressive()) {
            pearlCooldownTicks = 0;
            placeTicks = 0;
            breakTicks = 0;
            switchTicks = 0;
        } else if (noDelayMode.get()) {
            // Reset all cooldowns in no-delay mode
            pearlCooldownTicks = Math.max(0, pearlCooldownTicks - 1);
            placeTicks = 0;
            breakTicks = 0;
            switchTicks = 0;
        } else {
            // Normal cooldown updates
            if (pearlCooldownTicks > 0) pearlCooldownTicks--;
            if (placeTicks > 0) placeTicks--;
            if (breakTicks > 0) breakTicks--;
            if (switchTicks > 0) switchTicks--;
        }
    }

    /**
     * Instantly perform all batch crystal operations (placement and breaking) in a single tick.
     * Placement and breaking are both performed for all valid positions/crystals.
     * No batching delays, always uses packet breaking.
     */
    private void performInstantBatchCrystalOperations() {
        // Instantly break all valid crystals in the batch
        if (enableBreaking.get()) {
            performInstantBatchBreaking();
        }
        // Instantly place all valid crystals in the batch
        if (enablePlacement.get()) {
            performInstantBatchPlacement();
        }
        // Trapping logic can remain as is (optional, not batch-critical)
        if (enableTrapping.get()) {
            performSpeedOptimizedTrapping();
        }
    }

    /**
     * Instantly break all valid crystals in the current batch using packet-level breaking.
     * No artificial delays, always uses packets for maximum speed.
     */
    private boolean performInstantBatchBreaking() {
        targetCrystals.clear();
        findTargetCrystals();

        if (targetCrystals.isEmpty()) return false;

        boolean actionPerformed = false;
        for (EndCrystalEntity crystal : targetCrystals) {
            if (shouldBreakCrystal(crystal)) {
                // Always use packet breaking for speed
                performPacketBreak(crystal);
                actionPerformed = true;
            }
        }
        breakTicks = 0;
        return actionPerformed;
    }

    /**
     * Place one crystal per cooldown, in a fast, sequential manner.
     * Uses placeTicks as cooldown, and selects the correct hand.
     */
    private boolean performInstantBatchPlacement() {
        if (placeTicks > 0) return false;

        multiCrystalPositions.clear();
        findMultiCrystalPositions();

        if (multiCrystalPositions.isEmpty()) return false;

        // Place only one crystal per cooldown
        for (BlockPos pos : multiCrystalPositions) {
            if (!isValidCrystalPosition(pos) || !isPositionSafe(pos)) continue;
            if (placeCrystalWithHand(pos)) {
                placeTicks = placeDelay.get();
                return true;
            }
        }
        return false;
    }

    private boolean performSpeedOptimizedTrapping() {
        if (!enableTrapping.get() || currentTarget == null || isTrapping) return false;

        // Only trap if no valid crystal placement is available
        FindItemResult crystals = InvUtils.find(Items.END_CRYSTAL);
        if (crystals.found()) return false;

        // Only trap occasionally to not interfere with crystal operations
        if (tickCounter % 10 != 0) return false;

        handleTrapping();
        return true;
    }

    private boolean canPerformPacketAction() {
        if (!packetOptimization.get()) return true;

        long currentTime = System.currentTimeMillis();
        return currentTime - lastPacketTime >= packetDelay.get();
    }

    private void updatePacketTiming() {
        if (packetOptimization.get()) {
            lastPacketTime = System.currentTimeMillis();
        }
    }

    @EventHandler
    private void onRender3D(Render3DEvent event) {
        if (mc.player == null || mc.world == null) return;

        // Render crystal placement positions
        if (renderPlacement.get()) {
            for (int i = 0; i < multiCrystalPositions.size(); i++) {
                BlockPos pos = multiCrystalPositions.get(i);

                // Different colors for different priority levels
                Color color = placementColor.get();
                if (multiCrystalPositions.size() > 1) {
                    float alpha = 1.0f - (i * 0.2f); // Fade for lower priority positions
                    color = new Color(color.r, color.g, color.b, (int)(color.a * alpha));
                }

                event.renderer.box(pos, color, color, shapeMode.get(), 0);

                // Render damage numbers if enabled
                if (renderDamage.get() && positionDamageMap.containsKey(pos)) {
                    double damage = positionDamageMap.get(pos);
                    Vec3d textPos = Vec3d.ofCenter(pos).add(0, 1.2, 0);
                    renderDamageText(event, textPos, String.format("%.1f", damage), color);
                }
            }
        }

        // Defensive: Render pillar positions
        if (!renderPillarPositions.isEmpty()) {
            Color pillarColor = new Color(0, 200, 255, 80);
            for (BlockPos pos : renderPillarPositions) {
                for (int y = 0; y < defensePillarHeight.get(); y++) {
                    BlockPos pillarBlock = pos.up(y);
                    event.renderer.box(pillarBlock, pillarColor, pillarColor, shapeMode.get(), 0);
                }
            }
        }

        // Render chams and overlays for all End Crystal entities (comprehensive coverage)
        if (crystalVisible.get()) {
            for (Entity entity : mc.world.getEntities()) {
                if (!(entity instanceof EndCrystalEntity crystal)) continue;

                // Chams-style highlight: draw a semi-transparent fill and a bright outline
                event.renderer.box(
                    crystal.getBoundingBox().expand((float)(crystalChamsScale.get() - 1.0)),
                    crystalChamsColor.get(), // fill color (glow/transparency)
                    crystalChamsColor.get(),
                    ShapeMode.Sides,
                    0
                );
                event.renderer.box(
                    crystal.getBoundingBox().expand((float)(crystalChamsScale.get() - 1.0)),
                    breakingColor.get(), // outline color
                    breakingColor.get(),
                    ShapeMode.Lines,
                    0
                );

                // Overlay rendering (pseudo, as actual texture binding is renderer-dependent)
                if (!"None".equals(crystalChamsOverlay.get())) {
                    info("[LOG] Attempting to render overlay: " + crystalChamsOverlay.get() + " for crystal entity " + crystal.getId());
                    // Pseudo-code: event.renderer.drawOverlay(crystal, net.fabricmc.loader.api.FabricLoader.getInstance().getGameDir().resolve("Skylandia/chams_overlays/" + crystalChamsOverlay.get()).toString(), crystalChamsScale.get());
                    // Actual implementation depends on renderer API
                }

                // Render break attempts if debug mode and this is a breaking target
                if (debugMode.get() && targetCrystals.contains(crystal)) {
                    int attempts = crystalBreakAttempts.getOrDefault(crystal.getId(), 0);
                    if (attempts > 0) {
                        Vec3d textPos = crystal.getPos().add(0, 1.5, 0);
                        renderDamageText(event, textPos, "A:" + attempts, breakingColor.get());
                    }
                }
            }
        }

        // Render current target
        if (renderTarget.get() && currentTarget != null) {
            event.renderer.box(currentTarget.getBoundingBox().expand(0.1),
                targetColor.get(), targetColor.get(), shapeMode.get(), 0);
        }

        // Render movement prediction
        if (renderPrediction.get() && currentTarget != null && predictedTargetPos != null) {
            Vec3d currentPos = currentTarget.getPos();

            // Draw line from current position to predicted position
            event.renderer.line(currentPos.x, currentPos.y + 1, currentPos.z,
                predictedTargetPos.x, predictedTargetPos.y + 1, predictedTargetPos.z,
                predictionColor.get());

            // Draw predicted position box
            BlockPos predictedBlockPos = BlockPos.ofFloored(predictedTargetPos);
            event.renderer.box(predictedBlockPos, predictionColor.get(), predictionColor.get(),
                ShapeMode.Lines, 0);
        }

        // Render trap blocks
        if (renderTrapBlocks.get() && !trapBlockPositions.isEmpty()) {
            for (BlockPos pos : trapBlockPositions) {
                event.renderer.box(pos, trapColor.get(), trapColor.get(), shapeMode.get(), 0);
            }
        }

        // Render debug information
        if (debugMode.get()) {
            renderDebugVisualization(event);
        }
    }

    private void renderDamageText(Render3DEvent event, Vec3d pos, String text, Color color) {
        // This would need proper text rendering implementation
        // For now, we'll use a simple box to indicate damage level
        double size = Math.min(0.3, Double.parseDouble(text.replace("A:", "")) * 0.02);
        event.renderer.box(pos.x - size/2, pos.y - size/2, pos.z - size/2,
            pos.x + size/2, pos.y + size/2, pos.z + size/2,
            color, color, ShapeMode.Both, 0);
    }

    private void renderDebugVisualization(Render3DEvent event) {
        if (currentTarget == null) return;

        // Render search area around target
        BlockPos targetPos = currentTarget.getBlockPos();
        Color debugColor = new Color(255, 255, 255, 50);

        // Render placement search radius
        double range = placeRange.get();
        event.renderer.box(
            targetPos.getX() - range, targetPos.getY() - 2, targetPos.getZ() - range,
            targetPos.getX() + range, targetPos.getY() + 3, targetPos.getZ() + range,
            debugColor, debugColor, ShapeMode.Lines, 0
        );

        // Render target velocity vector
        Vec3d velocity = currentTarget.getVelocity();
        if (velocity.lengthSquared() > 0.01) {
            Vec3d start = currentTarget.getPos().add(0, 1, 0);
            Vec3d end = start.add(velocity.multiply(10));
            event.renderer.line(start.x, start.y, start.z, end.x, end.y, end.z,
                new Color(255, 255, 0, 200));
        }
    }

    @EventHandler
    private void onRender2D(Render2DEvent event) {
        if (!debugMode.get() || !renderDebugInfo.get()) return;
        if (mc.player == null || mc.world == null) return;

        TextRenderer textRenderer = TextRenderer.get();
        int y = 10;
        int lineHeight = (int) textRenderer.getHeight() + 2;

        // Module status
        textRenderer.begin(1, false, true);
        textRenderer.render("§4DemonCrystal Debug", 10, y, Color.WHITE);
        y += lineHeight;

        // Target information
        if (currentTarget != null) {
            textRenderer.render("§7Target: §f" + currentTarget.getName().getString(), 10, y, Color.WHITE);
            y += lineHeight;

            if (currentTarget instanceof LivingEntity living) {
                textRenderer.render("§7Health: §f" + String.format("%.1f", living.getHealth()), 10, y, Color.WHITE);
                y += lineHeight;
            }

            double distance = mc.player.distanceTo(currentTarget);
            textRenderer.render("§7Distance: §f" + String.format("%.1f", distance), 10, y, Color.WHITE);
            y += lineHeight;

            if (predictedTargetPos != null) {
                textRenderer.render("§7Predicted: §f" +
                    String.format("%.1f, %.1f, %.1f", predictedTargetPos.x, predictedTargetPos.y, predictedTargetPos.z),
                    10, y, Color.WHITE);
                y += lineHeight;
            }
        } else {
            textRenderer.render("§7Target: §cNone", 10, y, Color.WHITE);
            y += lineHeight;
        }

        // Crystal information
        textRenderer.render("§7Crystal Positions: §f" + multiCrystalPositions.size(), 10, y, Color.WHITE);
        y += lineHeight;

        textRenderer.render("§7Target Crystals: §f" + targetCrystals.size(), 10, y, Color.WHITE);
        y += lineHeight;

        textRenderer.render("§7Trap Blocks: §f" + trapBlockPositions.size(), 10, y, Color.WHITE);
        y += lineHeight;

        // Performance information
        textRenderer.render("§7Actions/Tick: §f" + actionsThisTick, 10, y, Color.WHITE);
        y += lineHeight;

        textRenderer.render("§7Burst Place: §f" + burstPlaceCount, 10, y, Color.WHITE);
        y += lineHeight;

        textRenderer.render("§7Burst Break: §f" + burstBreakCount, 10, y, Color.WHITE);
        y += lineHeight;

        // Safety information
        float health = mc.player.getHealth() + mc.player.getAbsorptionAmount();
        textRenderer.render("§7Health: §f" + String.format("%.1f", health), 10, y, Color.WHITE);
        y += lineHeight;

        int totems = InvUtils.find(Items.TOTEM_OF_UNDYING).count();
        textRenderer.render("§7Totems: §f" + totems, 10, y, Color.WHITE);
        y += lineHeight;

        // Timing information
        if (packetOptimization.get()) {
            long timeSinceLastPacket = System.currentTimeMillis() - lastPacketTime;
            textRenderer.render("§7Packet Delay: §f" + timeSinceLastPacket + "ms", 10, y, Color.WHITE);
            y += lineHeight;
        }

        // Mode information
        String mode = demonMode.get() ? "§4DEMON" : "§7Normal";
        textRenderer.render("§7Mode: " + mode, 10, y, Color.WHITE);
        y += lineHeight;

        if (speedMode.get()) {
            textRenderer.render("§7Speed: §aEnabled §7(" + tickOptimization.get() + "/tick)", 10, y, Color.WHITE);
            y += lineHeight;
        }

        textRenderer.end();
    }

    // === DEFENSIVE LOGIC ===

    @EventHandler
    private void onEntityAdded(EntityAddedEvent event) {
        // Only track Ender Crystals not placed by the player
        if (!(event.entity instanceof EndCrystalEntity)) return;
        BlockPos pos = event.entity.getBlockPos();
        // If the crystal is not at a position we recently placed, and not already in queue, treat as enemy
        if (!multiCrystalPositions.contains(pos) && !enemyCrystalPositions.contains(pos) && !recentlyDefended.contains(pos)) {
            if (enemyCrystalPositions.size() >= DEFENSE_PILLAR_QUEUE_SIZE) enemyCrystalPositions.poll();
            enemyCrystalPositions.offer(pos);
        }
    }

    private void handleDefensivePillars() {
        if (enemyCrystalPositions.isEmpty()) return;
        // Only place pillar blocks if no valid crystal placement is available
        FindItemResult crystals = InvUtils.find(Items.END_CRYSTAL);
        if (crystals.found()) return;

        // Use user-configurable block types for pillars
        List<Item> blockTypes = defensePillarBlockTypes.get();
        FindItemResult blockItem = InvUtils.find(blockTypes.toArray(new Item[0]));
        if (!blockItem.found()) return;

        renderPillarPositions.clear();
        pillarBlocksPlacedThisTick = 0;
        int maxPillarBlocksPerTick = 1; // Only place one pillar block per tick

        Iterator<BlockPos> it = enemyCrystalPositions.iterator();
        while (it.hasNext() && pillarBlocksPlacedThisTick < maxPillarBlocksPerTick) {
            BlockPos base = it.next();
            // Only build if not already recently defended
            if (recentlyDefended.contains(base)) continue;
            boolean built = buildPillarAtOptimized(base, blockItem);
            if (built) {
                recentlyDefended.add(base);
                renderPillarPositions.add(base);
                it.remove();
                pillarProgress.remove(base);
            }
        }
        // Clean up old entries
        if (recentlyDefended.size() > 32) {
            Iterator<BlockPos> clean = recentlyDefended.iterator();
            while (clean.hasNext() && recentlyDefended.size() > 32) clean.next(); clean.remove();
        }
        // Debug log for pillar placement rate
        if (debugMode.get()) {
            info("[DEBUG] Pillar blocks placed this tick: " + pillarBlocksPlacedThisTick);
        }
    }

    // Optimized: Only place one pillar block per tick per base
    private boolean buildPillarAtOptimized(BlockPos base, FindItemResult blockItem) {
        int pillarHeight = defensePillarHeight.get();
        int currentHeight = pillarProgress.getOrDefault(base, 0);

        // Find the next air block in the pillar
        while (currentHeight < pillarHeight) {
            BlockPos pos = base.up(currentHeight);
            if (!mc.world.getBlockState(pos).isAir()) {
                currentHeight++;
                continue;
            }
            // Check for solid block below for first block
            if (currentHeight == 0 && mc.world.getBlockState(pos.down()).isAir()) return false;
            // Switch to block
            if (switchMode.get() != SwitchMode.Disabled) {
                InvUtils.swap(blockItem.slot(), switchMode.get() == SwitchMode.Silent);
            }
            // Rotate if needed
            if (rotationMode.get() == RotationMode.Place || rotationMode.get() == RotationMode.Both) {
                Vec3d blockVec = Vec3d.ofCenter(pos);
                Vec3d playerVec = mc.player.getEyePos();
                Vec3d direction = blockVec.subtract(playerVec).normalize();
                float yaw = (float) Math.toDegrees(Math.atan2(direction.z, direction.x)) - 90f;
                float pitch = (float) -Math.toDegrees(Math.asin(direction.y));
                Rotations.rotate(yaw, pitch);
            }
            // Place the block (BPS throttle)
            if (!canPlaceBlockBPS()) return false;
            BlockHitResult result = new BlockHitResult(
                Vec3d.ofCenter(pos.down()),
                Direction.UP,
                pos.down(),
                false
            );
            if (mc.interactionManager.interactBlock(mc.player, Hand.MAIN_HAND, result).isAccepted()) {
                pillarProgress.put(base, currentHeight + 1);
                pillarBlocksPlacedThisTick++;
                // Only one block per tick
                return pillarProgress.get(base) >= pillarHeight;
            } else {
                // Placement failed, try again next tick
                pillarProgress.put(base, currentHeight);
                return false;
            }
        }
        // Pillar complete
        return true;
    }

    // === CORE METHODS ===
    private boolean isHyperAggressive() {
        return hyperAggressiveMode.get();
    }

    // Handles city mining logic (real implementation)
    private void handleCityMining() {
        // If not active, try to find a target and block to mine
        if (!cityMiningActive) {
            cityMiningTarget = null;
            cityMiningBlock = null;
            cityMiningPick = null;
            cityMiningProgress = 0.0f;

            // Find a valid player target within range
            PlayerEntity target = null;
            double bestDist = Double.MAX_VALUE;
            double miningRange = cityMiningRange.get();
            for (Entity entity : mc.world.getEntities()) {
                if (entity instanceof PlayerEntity player && entity != mc.player && !player.isCreative() && !player.isSpectator() && !Friends.get().isFriend(player)) {
                    double dist = mc.player.distanceTo(player);
                    if (dist < miningRange && dist < bestDist) {
                        target = player;
                        bestDist = dist;
                    }
                }
            }
            if (target == null) return;

            // Find a city block next to the target's feet
            BlockPos cityBlock = EntityUtils.getCityBlock(target);
            if (cityBlock == null || mc.player.squaredDistanceTo(cityBlock.getX() + 0.5, cityBlock.getY() + 0.5, cityBlock.getZ() + 0.5) > miningRange * miningRange) {
                return;
            }

            // Find a pickaxe in hotbar (user-configurable types)
            List<Item> pickaxeTypes = cityMiningPickaxeTypes.get();
            FindItemResult pick = InvUtils.find(itemStack -> pickaxeTypes.contains(itemStack.getItem()));
            if (!pick.isHotbar()) return;

            // Activate city mining state
            cityMiningActive = true;
            cityMiningTarget = target;
            cityMiningBlock = cityBlock;
            cityMiningPick = pick;
            cityMiningProgress = 0.0f;
        }

        // If active, continue mining
        if (cityMiningActive) {
            // Validate target and block
            double miningRange = cityMiningRange.get();
            if (cityMiningTarget == null || cityMiningBlock == null || cityMiningPick == null
                || cityMiningTarget.isRemoved()
                || mc.player.squaredDistanceTo(cityMiningBlock.getX() + 0.5, cityMiningBlock.getY() + 0.5, cityMiningBlock.getZ() + 0.5) > miningRange * miningRange) {
                cityMiningActive = false;
                cityMiningTarget = null;
                cityMiningBlock = null;
                cityMiningPick = null;
                cityMiningProgress = 0.0f;
                return;
            }

            // Ensure pickaxe is still in hotbar (user-configurable types)
            List<Item> pickaxeTypes = cityMiningPickaxeTypes.get();
            cityMiningPick = InvUtils.find(itemStack -> pickaxeTypes.contains(itemStack.getItem()));
            if (!cityMiningPick.isHotbar()) {
                cityMiningActive = false;
                return;
            }

            // Progress mining
            cityMiningProgress += BlockUtils.getBreakDelta(cityMiningPick.slot(), mc.world.getBlockState(cityMiningBlock));
            if (cityMiningProgress < 1.0f) {
                // Start mining packet
                Direction direction = BlockUtils.getDirection(cityMiningBlock);
                mc.getNetworkHandler().sendPacket(new PlayerActionC2SPacket(PlayerActionC2SPacket.Action.START_DESTROY_BLOCK, cityMiningBlock, direction));
                // Mining delay (if set)
                int delay = cityMiningDelay.get();
                if (delay > 0) {
                    try {
                        Thread.sleep(delay * 50);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
                return;
            }

            // Finish mining
            Direction direction = BlockUtils.getDirection(cityMiningBlock);
            InvUtils.swap(cityMiningPick.slot(), switchMode.get() == SwitchMode.Silent);
            if (rotationMode.get() == RotationMode.Both || rotationMode.get() == RotationMode.Break) {
                Rotations.rotate(Rotations.getYaw(cityMiningBlock), Rotations.getPitch(cityMiningBlock));
            }
            mc.getNetworkHandler().sendPacket(new PlayerActionC2SPacket(PlayerActionC2SPacket.Action.STOP_DESTROY_BLOCK, cityMiningBlock, direction));
            mc.getNetworkHandler().sendPacket(new HandSwingC2SPacket(Hand.MAIN_HAND));
            if (switchMode.get() == SwitchMode.Silent) InvUtils.swapBack();

            // Reset city mining state
            cityMiningActive = false;
            cityMiningTarget = null;
            cityMiningBlock = null;
            cityMiningPick = null;
            cityMiningProgress = 0.0f;
        }
    }

    private boolean shouldPause() {
        if (pauseOnEat.get() && mc.player.isUsingItem()) {
            Item item = mc.player.getMainHandStack().getItem();
            if (item == Items.GOLDEN_APPLE || item == Items.ENCHANTED_GOLDEN_APPLE) {
                return true;
            }
        }

        if (pauseOnMine.get() && mc.interactionManager != null && mc.interactionManager.isBreakingBlock()) {
            return true;
        }

        return false;
    }

    private boolean performSafetyChecks() {
        if (!safetyCheck.get()) return true;

        // Emergency health check
        if (emergencyDisable.get()) {
            float currentHealth = mc.player.getHealth() + mc.player.getAbsorptionAmount();
            if (currentHealth <= emergencyHealthThreshold.get()) {
                error("Emergency disable triggered - health too low: " + currentHealth);
                toggle();
                return false;
            }
        }

        // Totem safety check
        if (totemSafety.get()) {
            int totems = InvUtils.find(Items.TOTEM_OF_UNDYING).count();
            if (totems < minTotems.get()) {
                return false; // Don't disable, just pause operations
            }
        }

        // Armor safety check
        if (armorSafety.get() && !checkArmorSafety()) {
            return false;
        }

        // Hole safety check
        if (holeSafety.get() && !checkHoleSafety()) {
            return false;
        }

        return true;
    }

    private boolean checkArmorSafety() {
        for (ItemStack armorPiece : mc.player.getArmorItems()) {
            if (armorPiece.isEmpty()) continue;

            if (armorPiece.isDamageable()) {
                int maxDamage = armorPiece.getMaxDamage();
                int currentDamage = armorPiece.getDamage();
                double durabilityPercent = ((double) (maxDamage - currentDamage) / maxDamage) * 100;

                if (durabilityPercent < minArmorDurability.get()) {
                    return false;
                }
            }
        }
        return true;
    }

    private boolean checkHoleSafety() {
        BlockPos playerPos = mc.player.getBlockPos();

        // Check if player is in a hole (surrounded by solid blocks)
        if (surroundCheck.get()) {
            BlockPos[] surroundPositions = {
                playerPos.add(1, 0, 0),
                playerPos.add(-1, 0, 0),
                playerPos.add(0, 0, 1),
                playerPos.add(0, 0, -1)
            };

            for (BlockPos pos : surroundPositions) {
                if (!mc.world.getBlockState(pos).isSolidBlock(mc.world, pos)) {
                    return false; // Not fully surrounded
                }
            }
        }

        // Check for solid block below
        if (!mc.world.getBlockState(playerPos.down()).isSolidBlock(mc.world, playerPos.down())) {
            return false;
        }

        return true;
    }

    private boolean isPositionSafe(BlockPos pos) {
        if (!safetyCheck.get()) return true;

        double selfDamage = calculateCrystalDamage(pos, mc.player);

        // Basic safety checks
        if (selfDamage > maxSelfDamage.get()) return false;
        if (antiSuicide.get() && selfDamage >= mc.player.getHealth()) return false;

        // Enhanced safety with totems
        if (totemSafety.get()) {
            int totems = InvUtils.find(Items.TOTEM_OF_UNDYING).count();
            float currentHealth = mc.player.getHealth() + mc.player.getAbsorptionAmount();

            // More conservative with fewer totems
            double safetyMultiplier = Math.max(0.5, (double) totems / minTotems.get());
            double adjustedMaxDamage = maxSelfDamage.get() * safetyMultiplier;

            if (selfDamage > adjustedMaxDamage) return false;

            // Don't risk death even with totems if health is very low
            if (currentHealth <= 6 && selfDamage > currentHealth * 0.8) return false;
        }

        // Check if position would put us in danger from other crystals
        if (checkCrystalDangerAtPosition(pos)) return false;

        return true;
    }

    private boolean checkCrystalDangerAtPosition(BlockPos crystalPos) {
        Vec3d crystalVec = Vec3d.ofCenter(crystalPos);
        double totalDanger = 0;

        // Check danger from existing crystals
        for (Entity entity : mc.world.getEntities()) {
            if (!(entity instanceof EndCrystalEntity existingCrystal)) continue;
            if (entity.getPos().distanceTo(crystalVec) > 12) continue;

            double damage = calculateCrystalDamage(existingCrystal.getBlockPos(), mc.player);
            totalDanger += damage;
        }

        // Check danger from potential enemy crystal placements
        if (currentTarget != null) {
            BlockPos targetPos = currentTarget.getBlockPos();
            for (int x = -2; x <= 2; x++) {
                for (int z = -2; z <= 2; z++) {
                    BlockPos enemyPos = targetPos.add(x, 0, z);
                    if (isValidCrystalPosition(enemyPos)) {
                        double damage = calculateCrystalDamage(enemyPos, mc.player);
                        totalDanger += damage * 0.3; // Weight potential danger less
                    }
                }
            }
        }

        // Consider total danger unsafe if it exceeds our health
        float currentHealth = mc.player.getHealth() + mc.player.getAbsorptionAmount();
        return totalDanger > currentHealth * 0.9;
    }

   // --- Escape logic supporting chorus fruit and pearl ---
   private boolean shouldUseEscape() {
       if (autoPearlMode.get() == AutoPearlMode.Disabled || pearlCooldownTicks > 0) return false;

       boolean healthLow = false;
       boolean totemsLow = false;

       // Check health
       if (autoPearlMode.get() == AutoPearlMode.Health || autoPearlMode.get() == AutoPearlMode.Combined || autoPearlMode.get() == AutoPearlMode.Smart) {
           float health = mc.player.getHealth() + mc.player.getAbsorptionAmount();
           healthLow = health <= pearlHealthThreshold.get();
       }

       // Check totems
       if (autoPearlMode.get() == AutoPearlMode.Totems || autoPearlMode.get() == AutoPearlMode.Combined || autoPearlMode.get() == AutoPearlMode.Smart) {
           int totems = InvUtils.find(Items.TOTEM_OF_UNDYING).count();
           totemsLow = totems <= pearlTotemThreshold.get();
       }

       // Check if being targeted
       if (pearlOnlyWhenTargeted.get() && currentTarget == null) return false;

       boolean trigger = switch (autoPearlMode.get()) {
           case Health -> healthLow;
           case Totems -> totemsLow;
           case Combined -> healthLow && totemsLow;
           case Smart -> healthLow || totemsLow;
           default -> false;
       };

       if (!trigger) return false;

       // Check if escape method is available
       EscapeMethod method = escapeMethod.get();
       boolean hasChorus = InvUtils.find(Items.CHORUS_FRUIT).found();
       boolean hasPearl = InvUtils.find(Items.ENDER_PEARL).found();

       return switch (method) {
           case Chorus -> hasChorus;
           case Pearl -> hasPearl;
           case Both -> hasChorus || hasPearl;
       };
   }

   private void useEscape() {
       EscapeMethod method = escapeMethod.get();
       boolean hasChorus = InvUtils.find(Items.CHORUS_FRUIT).found();
       boolean hasPearl = InvUtils.find(Items.ENDER_PEARL).found();

       // Prioritize chorus fruit if both allowed and available
       if ((method == EscapeMethod.Chorus && hasChorus) || (method == EscapeMethod.Both && hasChorus)) {
           useChorusFruit();
       } else if ((method == EscapeMethod.Pearl && hasPearl) || (method == EscapeMethod.Both && hasPearl)) {
           usePearl();
       }
   }

   // --- Chorus fruit escape logic ---
   private void useChorusFruit() {
       FindItemResult chorus = InvUtils.find(Items.CHORUS_FRUIT);
       if (!chorus.found()) return;

       // Switch to chorus fruit
       if (switchMode.get() != SwitchMode.Disabled) {
           InvUtils.swap(chorus.slot(), switchMode.get() == SwitchMode.Silent);
       }

       // No rotation needed for chorus fruit
       mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
       pearlCooldownTicks = pearlCooldown.get();
       emergencyPearlUsed = true;

       info("Emergency chorus fruit escape activated!");
   }

   // --- Ender pearl escape logic (unchanged) ---
   private void usePearl() {
       FindItemResult pearl = InvUtils.find(Items.ENDER_PEARL);
       if (!pearl.found()) return;

       // Switch to pearl
       if (switchMode.get() != SwitchMode.Disabled) {
           InvUtils.swap(pearl.slot(), switchMode.get() == SwitchMode.Silent);
       }

       // Calculate throw direction (away from enemies)
       Vec3d throwDirection = calculatePearlDirection();
       if (throwDirection == null) return;

       // Rotate if needed
       if (rotationMode.get() != RotationMode.None) {
           float yaw = (float) Math.toDegrees(Math.atan2(throwDirection.z, throwDirection.x)) - 90f;
           float pitch = (float) -Math.toDegrees(Math.asin(throwDirection.y));
           Rotations.rotate(yaw, pitch);
       }

       // Throw pearl
       mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
       pearlCooldownTicks = pearlCooldown.get();
       emergencyPearlUsed = true;

       info("Emergency pearl escape activated!");
   }

    private Vec3d calculatePearlDirection() {
        if (currentTarget == null) return new Vec3d(0, 0, 1); // Default direction

        Vec3d playerPos = mc.player.getPos();
        Vec3d targetPos = currentTarget.getPos();
        Vec3d direction = playerPos.subtract(targetPos).normalize();

        // Add some upward angle for better escape
        return new Vec3d(direction.x, Math.max(0.2, direction.y), direction.z).normalize();
    }

    private void findTarget() {
        potentialTargets.clear();

        // Collect all valid targets
        for (Entity entity : mc.world.getEntities()) {
            if (!isValidTarget(entity)) continue;

            double distance = mc.player.distanceTo(entity);
            if (distance > targetRange.get()) continue;

            potentialTargets.add(entity);
        }

        if (potentialTargets.isEmpty()) {
            currentTarget = null;
            predictedTargetPos = null;
            return;
        }

        // Advanced targeting logic
        Entity newTarget = smartTargeting.get() ?
            findSmartTarget() : findBasicTarget();

        // Check if we should switch targets
        if (targetSwitching.get() && currentTarget != null && newTarget != currentTarget) {
            if (!shouldSwitchTarget(newTarget)) {
                newTarget = currentTarget;
            }
        }

        // Update target and prediction
        if (newTarget != currentTarget) {
            lastTarget = currentTarget;
            currentTarget = newTarget;
        }

        // Update movement prediction
        if (currentTarget != null && predictiveTargeting.get()) {
            updateTargetPrediction();
        }
    }

    private Entity findSmartTarget() {
        if (potentialTargets.isEmpty()) return null;

        Map<Entity, Double> targetScores = new HashMap<>();

        for (Entity entity : potentialTargets) {
            double score = calculateAdvancedTargetScore(entity);
            targetScores.put(entity, score);
        }

        // Return target with highest score
        return targetScores.entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse(null);
    }

    private Entity findBasicTarget() {
        if (potentialTargets.isEmpty()) return null;

        Entity bestTarget = null;
        double bestScore = Double.MAX_VALUE;

        for (Entity entity : potentialTargets) {
            double distance = mc.player.distanceTo(entity);
            double score = calculateTargetScore(entity, distance);

            if (score < bestScore) {
                bestScore = score;
                bestTarget = entity;
            }
        }

        return bestTarget;
    }

    private boolean shouldSwitchTarget(Entity newTarget) {
        if (currentTarget == null || newTarget == null) return true;

        double currentDamage = calculateMaxDamageToTarget(currentTarget);
        double newDamage = calculateMaxDamageToTarget(newTarget);

        // Switch if new target offers significantly more damage
        return newDamage > currentDamage + switchThreshold.get();
    }

    private double calculateAdvancedTargetScore(Entity entity) {
        double score = 0;

        // Base factors
        double distance = mc.player.distanceTo(entity);
        double health = entity instanceof LivingEntity living ? living.getHealth() : 20f;
        double maxDamage = calculateMaxDamageToTarget(entity);

        // Distance factor (closer is better, but not too close for safety)
        double optimalDistance = 6.0;
        double distanceFactor = Math.abs(distance - optimalDistance);
        score -= distanceFactor * 2;

        // Damage potential factor
        score += maxDamage * 10;

        // Health factor (prioritize low health targets)
        score += (20 - health) * 3;

        // Movement prediction factor
        if (predictiveTargeting.get()) {
            Vec3d velocity = entity.getVelocity();
            double speed = velocity.length();

            // Penalize fast-moving targets slightly
            score -= speed * 5;

            // Bonus for predictable movement
            if (speed > 0.1 && speed < 0.5) {
                score += 10; // Moderate speed is easier to predict
            }
        }

        // Armor factor for players
        if (entity instanceof PlayerEntity player) {
            int armorValue = getArmorValue(player);

            switch (targetPriority.get()) {
                case MostArmor -> score += armorValue * 5;
                case LeastArmor -> score -= armorValue * 5;
                default -> score -= armorValue * 2; // Generally prefer less armored targets
            }
        }

        // Face place bonus
        if (facePlaceMode.get() && health <= facePlaceHealth.get()) {
            score += 50; // High bonus for face place targets
        }

        // Continuity bonus (prefer current target to reduce switching)
        if (entity == currentTarget) {
            score += 15;
        }

        return score;
    }

    private double calculateMaxDamageToTarget(Entity target) {
        if (target == null) return 0;

        double maxDamage = 0;
        Vec3d targetPos = predictiveTargeting.get() && predictedTargetPos != null ?
            predictedTargetPos : target.getPos();
        BlockPos targetBlockPos = BlockPos.ofFloored(targetPos);

        // Check damage from potential crystal positions around target
        for (int x = -3; x <= 3; x++) {
            for (int z = -3; z <= 3; z++) {
                for (int y = -2; y <= 2; y++) {
                    BlockPos pos = targetBlockPos.add(x, y, z);

                    if (mc.player.getPos().distanceTo(Vec3d.ofCenter(pos)) > placeRange.get()) continue;
                    if (!isValidCrystalPosition(pos)) continue;

                    double damage = calculateCrystalDamage(pos, target);
                    double selfDamage = calculateCrystalDamage(pos, mc.player);

                    if (selfDamage > maxSelfDamage.get()) continue;
                    if (antiSuicide.get() && selfDamage >= mc.player.getHealth()) continue;

                    maxDamage = Math.max(maxDamage, damage);
                }
            }
        }

        return maxDamage;
    }

    private void updateTargetPrediction() {
        if (currentTarget == null) {
            predictedTargetPos = null;
            return;
        }

        Vec3d currentPos = currentTarget.getPos();
        Vec3d velocity = currentTarget.getVelocity();

        // Simple linear prediction
        double predictionTime = predictionTicks.get();

        // Adjust prediction based on accuracy setting
        double accuracy = predictionAccuracy.get();
        predictionTime *= accuracy;

        // Account for acceleration/deceleration
        Vec3d acceleration = Vec3d.ZERO;
        if (lastTarget == currentTarget && predictedTargetPos != null) {
            // Calculate acceleration from previous prediction
            Vec3d expectedPos = predictedTargetPos;
            Vec3d actualPos = currentPos;
            Vec3d error = actualPos.subtract(expectedPos);

            // Use error to improve next prediction
            acceleration = error.multiply(0.1); // Small correction factor
        }

        // Calculate predicted position
        predictedTargetPos = currentPos
            .add(velocity.multiply(predictionTime))
            .add(acceleration.multiply(predictionTime * predictionTime * 0.5));

        // Clamp prediction to reasonable bounds
        double maxPredictionDistance = targetRange.get() * 1.5;
        if (currentPos.distanceTo(predictedTargetPos) > maxPredictionDistance) {
            Vec3d direction = predictedTargetPos.subtract(currentPos).normalize();
            predictedTargetPos = currentPos.add(direction.multiply(maxPredictionDistance));
        }
    }

    private boolean isValidTarget(Entity entity) {
        if (!(entity instanceof LivingEntity living)) return false;
        if (entity == mc.player) return false;
        if (!targetEntities.get().contains(entity.getType())) return false;
        if (entity.isRemoved() || !entity.isAlive()) return false;

        if (entity instanceof PlayerEntity player && Friends.get().isFriend(player)) return false;

        if (ignoreNaked.get() && entity instanceof PlayerEntity player) {
            // Check if player has armor
            boolean hasArmor = false;
            for (ItemStack stack : player.getArmorItems()) {
                if (!stack.isEmpty()) {
                    hasArmor = true;
                    break;
                }
            }
            if (!hasArmor) return false;
        }

        return true;
    }

    private double calculateTargetScore(Entity entity, double distance) {
        return switch (targetPriority.get()) {
            case Closest -> distance;
            case LowestHealth -> entity instanceof LivingEntity living ? living.getHealth() : distance;
            case HighestDamage -> -calculateDamageToTarget(entity); // Negative for highest
            case MostArmor -> entity instanceof PlayerEntity player ? -getArmorValue(player) : distance;
            case LeastArmor -> entity instanceof PlayerEntity player ? getArmorValue(player) : distance;
        };
    }

    private double calculateDamageToTarget(Entity target) {
        // Find closest crystal position to target for damage calculation
        BlockPos targetPos = target.getBlockPos();
        BlockPos crystalPos = targetPos.add(1, 0, 0); // Simplified
        return calculateCrystalDamage(crystalPos, target);
    }

    private double calculateCrystalDamage(BlockPos crystalPos, Entity target) {
        if (target == null) return 0;

        Vec3d crystalVec = Vec3d.ofCenter(crystalPos);
        Vec3d targetVec = target.getPos();

        // Calculate distance
        double distance = crystalVec.distanceTo(targetVec);
        if (distance > 12) return 0; // Max crystal damage range

        // Base damage calculation (simplified)
        double damage = 12 * (1 - (distance / 12));

        // Apply armor reduction if it's a player
        if (target instanceof PlayerEntity player) {
            int armor = getArmorValue(player);
            damage = damage * (1 - (armor * 0.04)); // 4% reduction per armor point
        }

        // Ensure minimum damage
        return Math.max(0, damage);
    }

    private int getArmorValue(PlayerEntity player) {
        int armor = 0;
        for (ItemStack stack : player.getArmorItems()) {
            if (stack.getItem() instanceof ArmorItem) {
                armor += 1; // Simplified armor counting
            }
        }
        return armor;
    }

    private void handleTrapping() {
        if (!enableTrapping.get() || currentTarget == null) return;
        if (isTrapping) return; // Prevent concurrent trapping

        // Check if we should trap this target
        if (trapOnlyLowHealth.get()) {
            if (!(currentTarget instanceof LivingEntity living) ||
                living.getHealth() > trapHealthThreshold.get()) {
                return;
            }
        }

        trapBlockPositions.clear();
        findTrapPositions();

        if (!trapBlockPositions.isEmpty()) {
            isTrapping = true;
            placeTrapBlocks();
            isTrapping = false;
        }
    }

    private void findTrapPositions() {
        if (currentTarget == null) return;

        Vec3d targetPos = predictMovement.get() && predictedTargetPos != null ?
            predictedTargetPos : currentTarget.getPos();
        BlockPos targetBlockPos = BlockPos.ofFloored(targetPos);

        List<BlockPos> candidates = new ArrayList<>();

        // Find positions around target for trapping
        int range = trapRange.get().intValue();
        for (int x = -range; x <= range; x++) {
            for (int z = -range; z <= range; z++) {
                for (int y = -1; y <= 3; y++) {
                    BlockPos pos = targetBlockPos.add(x, y, z);

                    // Skip positions too far from player
                    if (mc.player.getPos().distanceTo(Vec3d.ofCenter(pos)) > placeRange.get()) continue;

                    // Check if this is a valid trap position
                    if (isValidTrapPosition(pos, targetBlockPos)) {
                        candidates.add(pos);
                    }
                }
            }
        }

        // Select best trap positions
        selectBestTrapPositions(candidates, targetBlockPos);
    }

    private boolean isValidTrapPosition(BlockPos pos, BlockPos targetPos) {
        // Must be air block
        if (!mc.world.getBlockState(pos).isAir()) return false;

        // Must have solid block below (for placement)
        if (mc.world.getBlockState(pos.down()).isAir()) return false;

        // Check if position would actually trap the target
        double distance = pos.getSquaredDistance(targetPos);
        if (distance > trapRange.get() * trapRange.get()) return false;

        // Prioritize positions that block movement
        if (trapSides.get()) {
            // Check if this position blocks horizontal movement
            int dx = pos.getX() - targetPos.getX();
            int dz = pos.getZ() - targetPos.getZ();
            if (Math.abs(dx) <= 1 && Math.abs(dz) <= 1 && pos.getY() == targetPos.getY()) {
                return true;
            }
        }

        if (trapAbove.get()) {
            // Check if this position blocks vertical movement
            if (pos.getX() == targetPos.getX() && pos.getZ() == targetPos.getZ() &&
                pos.getY() > targetPos.getY() && pos.getY() <= targetPos.getY() + 2) {
                return true;
            }
        }

        return false;
    }

    private void selectBestTrapPositions(List<BlockPos> candidates, BlockPos targetPos) {
        if (candidates.isEmpty()) return;

        Map<BlockPos, Double> positionScores = new HashMap<>();
        Vec3d targetVelocity = currentTarget.getVelocity();

        for (BlockPos pos : candidates) {
            double score = calculateTrapScore(pos, targetPos, targetVelocity);
            if (score > 0) {
                positionScores.put(pos, score);
            }
        }

        // Sort by score and select top positions
        List<Map.Entry<BlockPos, Double>> sortedPositions = positionScores.entrySet()
            .stream()
            .sorted(Map.Entry.<BlockPos, Double>comparingByValue().reversed())
            .toList();

        int maxBlocks = Math.min(maxTrapBlocks.get(), sortedPositions.size());
        for (int i = 0; i < maxBlocks; i++) {
            trapBlockPositions.add(sortedPositions.get(i).getKey());
        }
    }

    private double calculateTrapScore(BlockPos pos, BlockPos targetPos, Vec3d targetVelocity) {
        double score = 0;

        // Base score - closer to target is better
        double distance = Math.sqrt(pos.getSquaredDistance(targetPos));
        score += (trapRange.get() - distance) * 10;

        // Bonus for blocking escape routes
        if (smartTrapping.get() && targetVelocity.lengthSquared() > 0.01) {
            Vec3d escapeDirection = targetVelocity.normalize();
            Vec3d posDirection = Vec3d.ofCenter(pos).subtract(Vec3d.ofCenter(targetPos)).normalize();
            double alignment = posDirection.dotProduct(escapeDirection);
            if (alignment > 0.5) {
                score += 50; // High bonus for blocking escape
            }
        }

        // Bonus for strategic positions
        int dx = Math.abs(pos.getX() - targetPos.getX());
        int dz = Math.abs(pos.getZ() - targetPos.getZ());
        int dy = pos.getY() - targetPos.getY();

        // Prioritize positions that form walls
        if (dx <= 1 && dz <= 1 && dy == 0) score += 30; // Side blocking
        if (dx == 0 && dz == 0 && dy > 0 && dy <= 2) score += 40; // Above blocking

        // Penalty for positions that might block our own crystals
        for (BlockPos crystalPos : multiCrystalPositions) {
            if (pos.isWithinDistance(crystalPos, 2)) {
                score -= 20;
            }
        }

        return score;
    }

    private void placeTrapBlocks() {
        FindItemResult obsidian = InvUtils.find(Items.OBSIDIAN);
        if (!obsidian.found()) {
            // Try other solid blocks
            obsidian = InvUtils.find(Items.COBBLESTONE, Items.STONE, Items.NETHERRACK);
            if (!obsidian.found()) return;
        }

        for (BlockPos pos : trapBlockPositions) {
            if (!canPlaceBlockBPS()) continue;
            if (placeTrapBlock(pos, obsidian)) {
                // Small delay between trap block placements
                try {
                    Thread.sleep(50);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
    }

    private boolean placeTrapBlock(BlockPos pos, FindItemResult blockItem) {
        // Switch to block
        if (switchMode.get() != SwitchMode.Disabled) {
            InvUtils.swap(blockItem.slot(), switchMode.get() == SwitchMode.Silent);
        }

        // Rotate towards position if needed
        if (rotationMode.get() == RotationMode.Place || rotationMode.get() == RotationMode.Both) {
            Vec3d blockVec = Vec3d.ofCenter(pos);
            Vec3d playerVec = mc.player.getEyePos();
            Vec3d direction = blockVec.subtract(playerVec).normalize();

            float yaw = (float) Math.toDegrees(Math.atan2(direction.z, direction.x)) - 90f;
            float pitch = (float) -Math.toDegrees(Math.asin(direction.y));
            Rotations.rotate(yaw, pitch);
        }

        // Place the block
        BlockHitResult result = new BlockHitResult(
            Vec3d.ofCenter(pos.down()),
            Direction.UP,
            pos.down(),
            false
        );

        return mc.interactionManager.interactBlock(mc.player, Hand.MAIN_HAND, result).isAccepted();
    }

    private void handlePlacement() {
        if (placeTicks > 0) return;

        multiCrystalPositions.clear();

        if (multiCrystalMode.get() == MultiCrystalMode.Disabled) {
            // Single crystal placement
            BlockPos pos = findBestCrystalPosition();
            if (pos != null) {
                multiCrystalPositions.add(pos);
                info("Found crystal position: " + pos.toString());
            } else {
                info("No valid crystal position found");
            }
        } else {
            // Multi-crystal placement
            findMultiCrystalPositions();
            info("Multi-crystal positions found: " + multiCrystalPositions.size());
        }

        // Place crystals
        for (BlockPos pos : multiCrystalPositions) {
            info("Attempting to place crystal at: " + pos.toString());
            if (placeCrystalWithHand(pos)) {
                info("Crystal placed successfully!");
                placeTicks = placeDelay.get();
                if (multiCrystalDelay.get() > 0) {
                    try {
                        Thread.sleep(multiCrystalDelay.get() * 50); // Convert ticks to ms
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            } else {
                info("Failed to place crystal");
            }
        }
    }

    private void handleBreaking() {
        if (breakTicks > 0) return;

        targetCrystals.clear();
        burstTargets.clear();

        // Find all crystals in range
        findTargetCrystals();

        if (targetCrystals.isEmpty()) return;

        // Prioritize crystals for breaking
        prioritizeCrystals();

        if (burstBreaking.get() && !burstTargets.isEmpty()) {
            performBurstBreaking();
        } else {
            // Standard breaking
            for (EndCrystalEntity crystal : targetCrystals) {
                if (shouldBreakCrystal(crystal) && breakCrystal(crystal)) {
                    breakTicks = breakDelay.get();
                    burstBreakCount++;
                    break; // Break one at a time in standard mode
                }
            }
        }
    }

    private void findTargetCrystals() {
        for (Entity entity : mc.world.getEntities()) {
            if (!(entity instanceof EndCrystalEntity crystal)) continue;
            if (mc.player.distanceTo(crystal) > breakRange.get()) continue;
            if (crystal.isRemoved()) continue;

            // Check if crystal is worth breaking
            if (shouldBreakCrystal(crystal)) {
                targetCrystals.add(crystal);
            }
        }
    }

    private boolean shouldBreakCrystal(EndCrystalEntity crystal) {
        // Check damage to current target
        double damageToTarget = 0;
        if (currentTarget != null) {
            damageToTarget = calculateCrystalDamage(crystal.getBlockPos(), currentTarget);
        }

        // Check damage to any valid targets if no current target
        if (damageToTarget < minDamageBreak.get()) {
            for (Entity entity : mc.world.getEntities()) {
                if (isValidTarget(entity)) {
                    double damage = calculateCrystalDamage(crystal.getBlockPos(), entity);
                    if (damage >= minDamageBreak.get()) {
                        damageToTarget = damage;
                        break;
                    }
                }
            }
        }

        // Check if crystal deals enough damage
        if (damageToTarget < minDamageBreak.get()) return false;

        // Check self damage
        double selfDamage = calculateCrystalDamage(crystal.getBlockPos(), mc.player);
        if (antiSuicide.get() && selfDamage >= mc.player.getHealth()) return false;
        if (selfDamage > maxSelfDamage.get()) return false;

        // Check break attempts
        int attempts = crystalBreakAttempts.getOrDefault(crystal.getId(), 0);
        if (attempts >= breakAttempts.get()) return false;

        // Check timing for packet breaking
        if (packetBreaking.get()) {
            long lastAttack = crystalLastAttack.getOrDefault(crystal.getId(), 0L);
            if (System.currentTimeMillis() - lastAttack < 50) return false; // Minimum 50ms between attacks
        }

        return true;
    }

    private void prioritizeCrystals() {
        // Sort crystals by priority: damage to target, proximity to opponents, then distance to player
        targetCrystals.sort((a, b) -> {
            double damageA = currentTarget != null ? calculateCrystalDamage(a.getBlockPos(), currentTarget) : 0;
            double damageB = currentTarget != null ? calculateCrystalDamage(b.getBlockPos(), currentTarget) : 0;

            // Compute proximity score: lower is better (closer to any valid opponent)
            double proximityA = getNearestOpponentDistance(a);
            double proximityB = getNearestOpponentDistance(b);

            // Higher damage first
            if (Math.abs(damageA - damageB) > 1.0) {
                return Double.compare(damageB, damageA);
            }

            // If damage is similar, prioritize crystals closer to opponents
            if (Math.abs(proximityA - proximityB) > 0.5) {
                return Double.compare(proximityA, proximityB);
            }

            // If still similar, prioritize closer to player
            double distA = mc.player.distanceTo(a);
            double distB = mc.player.distanceTo(b);
            return Double.compare(distA, distB);
        });

        // Select crystals for burst breaking
        if (burstBreaking.get()) {
            int burstSize = Math.min(burstCount.get(), targetCrystals.size());
            for (int i = 0; i < burstSize; i++) {
                burstTargets.add(targetCrystals.get(i));
            }
        }
    }

    // Returns the distance from the crystal to the nearest valid opponent (excluding self)
    private double getNearestOpponentDistance(EndCrystalEntity crystal) {
        double minDist = Double.MAX_VALUE;
        Vec3d crystalPos = crystal.getPos();
        for (Entity entity : mc.world.getEntities()) {
            if (isValidTarget(entity)) {
                double dist = crystalPos.distanceTo(entity.getPos());
                if (dist < minDist) minDist = dist;
            }
        }
        return minDist;
    }

    private void performBurstBreaking() {
        if (burstTargets.isEmpty()) return;

        long currentTime = System.currentTimeMillis();

        // Check if we can perform burst (timing)
        if (currentTime - lastBreakTime < burstDelay.get()) return;

        // Switch to appropriate tool if needed
        if (antiWeakness.get() && mc.player.hasStatusEffect(StatusEffects.WEAKNESS)) {
            FindItemResult tool = InvUtils.find(itemStack ->
                itemStack.getItem() instanceof SwordItem ||
                itemStack.getItem() instanceof AxeItem ||
                itemStack.getItem() instanceof PickaxeItem
            );
            if (tool.found() && switchMode.get() != SwitchMode.Disabled) {
                InvUtils.swap(tool.slot(), switchMode.get() == SwitchMode.Silent);
            }
        }

        // Perform burst attacks
        for (EndCrystalEntity crystal : burstTargets) {
            if (crystal.isRemoved()) continue;

            if (packetBreaking.get()) {
                performPacketBreak(crystal);
            } else {
                performStandardBreak(crystal);
            }

            // Update tracking
            crystalBreakAttempts.put(crystal.getId(),
                crystalBreakAttempts.getOrDefault(crystal.getId(), 0) + 1);
            crystalLastAttack.put(crystal.getId(), currentTime);

            burstBreakCount++;

            // Small delay between burst attacks
            if (burstDelay.get() > 0) {
                try {
                    Thread.sleep(burstDelay.get() / burstTargets.size());
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        lastBreakTime = currentTime;
        breakTicks = breakDelay.get();
    }

    /**
     * Finds the best crystal position, prioritizing positions directly adjacent to the opponent.
     * If no adjacent positions are valid, selects the next closest valid position.
     */
    private BlockPos findBestCrystalPosition() {
        if (currentTarget == null) return null;
    
        BlockPos targetPos = currentTarget.getBlockPos();
        BlockPos bestPos = null;
        double bestDamage = 0;
    
        // 1. Check all directly adjacent positions (N, S, E, W, Up, Down)
        BlockPos[] adjacentOffsets = {
            targetPos.add(1, 0, 0),
            targetPos.add(-1, 0, 0),
            targetPos.add(0, 0, 1),
            targetPos.add(0, 0, -1),
            targetPos.add(0, 1, 0),
            targetPos.add(0, -1, 0)
        };
    
        for (BlockPos pos : adjacentOffsets) {
            if (!isValidCrystalPosition(pos)) continue;
            if (mc.player.getPos().distanceTo(Vec3d.ofCenter(pos)) > placeRange.get()) continue;
    
            double damage = calculateCrystalDamage(pos, currentTarget);
            double selfDamage = calculateCrystalDamage(pos, mc.player);
    
            if (damage < minDamagePlace.get()) continue;
            if (antiSuicide.get() && selfDamage >= mc.player.getHealth()) continue;
            if (selfDamage > maxSelfDamage.get()) continue;
    
            if (damage > bestDamage) {
                bestDamage = damage;
                bestPos = pos;
            }
        }
    
        // If a valid adjacent position was found, return it
        if (bestPos != null) return bestPos;
    
        // 2. If no adjacent positions are valid, search all positions within range and pick the closest valid one
        double closestDist = Double.MAX_VALUE;
        for (int x = -3; x <= 3; x++) {
            for (int z = -3; z <= 3; z++) {
                for (int y = -2; y <= 2; y++) {
                    BlockPos pos = targetPos.add(x, y, z);
    
                    if (!isValidCrystalPosition(pos)) continue;
                    if (mc.player.getPos().distanceTo(Vec3d.ofCenter(pos)) > placeRange.get()) continue;
    
                    double damage = calculateCrystalDamage(pos, currentTarget);
                    double selfDamage = calculateCrystalDamage(pos, mc.player);
    
                    if (damage < minDamagePlace.get()) continue;
                    if (antiSuicide.get() && selfDamage >= mc.player.getHealth()) continue;
                    if (selfDamage > maxSelfDamage.get()) continue;
    
                    double dist = pos.getSquaredDistance(targetPos);
                    if (dist < closestDist) {
                        closestDist = dist;
                        bestDamage = damage;
                        bestPos = pos;
                    }
                }
            }
        }
    
        return bestPos;
    }

    /**
     * Finds multi-crystal positions, prioritizing positions directly adjacent to the opponent first.
     * If not enough adjacent positions are valid, fills remaining slots with the next closest valid positions.
     */
    private void findMultiCrystalPositions() {
        if (currentTarget == null) return;
    
        multiCrystalPositions.clear();
        positionDamageMap.clear();
    
        int targetCount = calculateTargetCrystalCount();
        if (targetCount <= 1) {
            // Single crystal mode
            BlockPos bestPos = findBestCrystalPosition();
            if (bestPos != null) {
                multiCrystalPositions.add(bestPos);
            }
            return;
        }
    
        // 1. Collect all valid adjacent positions first
        BlockPos targetPos = currentTarget.getBlockPos();
        BlockPos[] adjacentOffsets = {
            targetPos.add(1, 0, 0),
            targetPos.add(-1, 0, 0),
            targetPos.add(0, 0, 1),
            targetPos.add(0, 0, -1),
            targetPos.add(0, 1, 0),
            targetPos.add(0, -1, 0)
        };
        for (BlockPos pos : adjacentOffsets) {
            if (multiCrystalPositions.size() >= targetCount) break;
            if (!isValidCrystalPosition(pos) || !isPositionSafe(pos)) continue;
            double damage = calculateCrystalDamage(pos, currentTarget);
            if (damage >= minDamagePlace.get()) {
                multiCrystalPositions.add(pos);
                positionDamageMap.put(pos, damage);
            }
        }
    
        // 2. If not enough, fill with closest valid positions
        if (multiCrystalPositions.size() < targetCount) {
            List<BlockPos> candidatePositions = findCandidatePositions();
            // Sort by distance to target
            candidatePositions.sort(Comparator.comparingDouble(p -> p.getSquaredDistance(targetPos)));
            for (BlockPos pos : candidatePositions) {
                if (multiCrystalPositions.size() >= targetCount) break;
                if (multiCrystalPositions.contains(pos)) continue;
                if (!isValidCrystalPosition(pos) || !isPositionSafe(pos)) continue;
                double damage = calculateCrystalDamage(pos, currentTarget);
                if (damage >= minDamagePlace.get()) {
                    multiCrystalPositions.add(pos);
                    positionDamageMap.put(pos, damage);
                }
            }
        }
    
        // If we still don't have enough positions, try fallback patterns
        if (multiCrystalPositions.size() < targetCount) {
            findFallbackPositions(targetCount - multiCrystalPositions.size());
        }
    }

    private int calculateTargetCrystalCount() {
        return switch (multiCrystalMode.get()) {
            case Disabled -> 1;
            case Dual -> 2;
            case Triple -> 3;
            case Quad -> 4;
            case Penta -> 5;
            case Burst -> Math.min(maxCrystals.get(), 5);
            case Adaptive -> calculateAdaptiveCount();
        };
    }

    private int calculateAdaptiveCount() {
        if (currentTarget == null) return 2;

        double distance = mc.player.distanceTo(currentTarget);
        float targetHealth = currentTarget instanceof LivingEntity living ? living.getHealth() : 20f;

        // Close combat with low health target - use maximum crystals
        if (distance < 4 && targetHealth < 10) return Math.min(maxCrystals.get(), 5);

        // Medium range combat - use moderate crystal count
        if (distance < 8) return Math.min(maxCrystals.get(), 3);

        // Long range - use fewer crystals for precision
        return Math.min(maxCrystals.get(), 2);
    }

    private List<BlockPos> findCandidatePositions() {
        List<BlockPos> candidates = new ArrayList<>();
        if (currentTarget == null) return candidates;

        Vec3d targetPos = predictMovement.get() && predictedTargetPos != null ?
            predictedTargetPos : currentTarget.getPos();
        BlockPos targetBlockPos = BlockPos.ofFloored(targetPos);

        // Search in expanding radius around target
        int searchRadius = (int) Math.ceil(placeRange.get());
        for (int radius = 1; radius <= searchRadius; radius++) {
            for (int x = -radius; x <= radius; x++) {
                for (int z = -radius; z <= radius; z++) {
                    for (int y = -2; y <= 3; y++) {
                        if (Math.abs(x) != radius && Math.abs(z) != radius && y != -2 && y != 3) continue;

                        BlockPos pos = targetBlockPos.add(x, y, z);
                        if (mc.player.getPos().distanceTo(Vec3d.ofCenter(pos)) <= placeRange.get()) {
                            candidates.add(pos);
                        }
                    }
                }
            }
        }

        return candidates;
    }

    private List<BlockPos> selectOptimalPositions(List<BlockPos> candidates, int targetCount) {
        List<BlockPos> selected = new ArrayList<>();
        Map<BlockPos, Double> damageScores = new HashMap<>();
    
        // Calculate damage scores for all candidates
        for (BlockPos pos : candidates) {
            if (!isValidCrystalPosition(pos)) continue;
    
            double damage = calculateCrystalDamage(pos, currentTarget);
            double selfDamage = calculateCrystalDamage(pos, mc.player);
    
            if (damage < minDamagePlace.get()) continue;
            if (antiSuicide.get() && selfDamage >= mc.player.getHealth()) continue;
            if (selfDamage > maxSelfDamage.get()) continue;
    
            // Score based on damage to target minus self damage
            double score = damage - (selfDamage * 0.5);
            damageScores.put(pos, score);
        }
    
        // Sort by proximity to opponent's feet, then by damage score
        BlockPos targetFeet = currentTarget != null ? currentTarget.getBlockPos() : BlockPos.ORIGIN;
        List<Map.Entry<BlockPos, Double>> sortedEntries = damageScores.entrySet()
            .stream()
            .sorted((a, b) -> {
                int yComp = Integer.compare(a.getKey().getY(), b.getKey().getY());
                if (yComp != 0) return yComp;
                double da = a.getKey().getSquaredDistance(targetFeet);
                double db = b.getKey().getSquaredDistance(targetFeet);
                if (da != db) return Double.compare(da, db);
                return Double.compare(b.getValue(), a.getValue());
            })
            .toList();
    
        // Select positions avoiding overlap if enabled
        for (Map.Entry<BlockPos, Double> entry : sortedEntries) {
            if (selected.size() >= targetCount) break;
    
            BlockPos pos = entry.getKey();
            boolean shouldAdd = true;
    
            if (avoidOverlap.get()) {
                for (BlockPos existing : selected) {
                    if (pos.isWithinDistance(existing, 4.0)) {
                        // Check if this position provides significantly more damage
                        double existingDamage = damageScores.getOrDefault(existing, 0.0);
                        double newDamage = entry.getValue();
                        if (newDamage <= existingDamage * 1.2) {
                            shouldAdd = false;
                            break;
                        }
                    }
                }
            }
    
            if (shouldAdd) {
                selected.add(pos);
            }
        }
    
        return selected;
    }

    private List<BlockPos> applyPlacementPattern(List<BlockPos> positions, int targetCount) {
        if (positions.size() <= 1 || currentTarget == null) return positions;

        Vec3d targetPos = currentTarget.getPos();
        BlockPos targetBlockPos = BlockPos.ofFloored(targetPos);

        return switch (placementPattern.get()) {
            case Surrounding -> arrangeSurrounding(positions, targetBlockPos, targetCount);
            case Linear -> arrangeLinear(positions, targetBlockPos, targetCount);
            case Cross -> arrangeCross(positions, targetBlockPos, targetCount);
            case Diamond -> arrangeDiamond(positions, targetBlockPos, targetCount);
            case Trap -> arrangeTrap(positions, targetBlockPos, targetCount);
            case Optimal -> positions; // Already optimized by damage
        };
    }



    private void findFallbackPositions(int needed) {
        if (currentTarget == null || needed <= 0) return;

        BlockPos targetPos = currentTarget.getBlockPos();

        // Simple fallback - find any valid positions near target
        for (int radius = 1; radius <= 4 && multiCrystalPositions.size() < maxCrystals.get(); radius++) {
            for (int x = -radius; x <= radius; x++) {
                for (int z = -radius; z <= radius; z++) {
                    if (multiCrystalPositions.size() >= maxCrystals.get()) return;

                    BlockPos pos = targetPos.add(x, 0, z);
                    if (isValidCrystalPosition(pos) && isPositionSafe(pos)) {
                        double damage = calculateCrystalDamage(pos, currentTarget);
                        if (damage >= minDamagePlace.get() * 0.7) { // Lower threshold for fallback
                            multiCrystalPositions.add(pos);
                            positionDamageMap.put(pos, damage);
                            needed--;
                            if (needed <= 0) return;
                        }
                    }
                }
            }
        }
    }

    private boolean isValidCrystalPosition(BlockPos pos) {
        // Check if position is valid for crystal placement
        if (!mc.world.getBlockState(pos).isAir()) return false;
        if (!mc.world.getBlockState(pos.up()).isAir()) return false;

        // User-configurable base block check
        BlockPos basePos = pos.down();
        Block baseBlock = mc.world.getBlockState(basePos).getBlock();
        if (!validCrystalBaseBlocks.get().contains(baseBlock)) return false;

        // Check for existing crystals
        for (Entity entity : mc.world.getEntities()) {
            if (entity instanceof EndCrystalEntity && entity.getBlockPos().equals(pos)) {
                return false;
            }
        }

        // Check if we can place here (1.12 placement check)
        if (!placement112.get()) {
            // 1.13+ placement - check for entities in the way
            Box box = new Box(pos.getX(), pos.getY(), pos.getZ(), pos.getX() + 1, pos.getY() + 2, pos.getZ() + 1);
            if (!mc.world.getOtherEntities(null, box).isEmpty()) return false;
        }

        return true;
    }

    /**
     * Place a crystal at the given position using the correct hand (main/offhand).
     * Returns true if placement was attempted.
     */
    private boolean placeCrystalWithHand(BlockPos pos) {
        Hand handToUse = null;
        ItemStack mainHandStack = mc.player.getMainHandStack();
        ItemStack offHandStack = mc.player.getOffHandStack();

        if (mainHandStack.getItem() == Items.END_CRYSTAL) {
            handToUse = Hand.MAIN_HAND;
        } else if (offHandStack.getItem() == Items.END_CRYSTAL) {
            handToUse = Hand.OFF_HAND;
        } else {
            // Fallback: try to swap to crystal in hotbar
            FindItemResult crystal = InvUtils.find(Items.END_CRYSTAL);
            if (!crystal.found()) return false;
            if (switchMode.get() != SwitchMode.Disabled) {
                InvUtils.swap(crystal.slot(), switchMode.get() == SwitchMode.Silent);
                if (switchDelay.get() > 0) {
                    switchTicks = switchDelay.get();
                    return false; // Wait for switch delay
                }
            }
            handToUse = Hand.MAIN_HAND;
        }

        // Rotate towards position if needed
        if (rotationMode.get() == RotationMode.Place || rotationMode.get() == RotationMode.Both) {
            Vec3d crystalVec = Vec3d.ofCenter(pos);
            Vec3d playerVec = mc.player.getEyePos();
            Vec3d direction = crystalVec.subtract(playerVec).normalize();

            float yaw = (float) Math.toDegrees(Math.atan2(direction.z, direction.x)) - 90f;
            float pitch = (float) -Math.toDegrees(Math.asin(direction.y));
            Rotations.rotate(yaw, pitch);
        }

        // Place the crystal
        BlockHitResult result = new BlockHitResult(
            Vec3d.ofCenter(pos.down()),
            Direction.UP,
            pos.down(),
            false
        );

        mc.interactionManager.interactBlock(mc.player, handToUse, result);
        return true;
    }

    private boolean breakCrystal(EndCrystalEntity crystal) {
        if (packetBreaking.get()) {
            return performPacketBreak(crystal);
        } else {
            return performStandardBreak(crystal);
        }
    }

    private boolean performPacketBreak(EndCrystalEntity crystal) {
        // Rotate if needed
        if (rotationMode.get() == RotationMode.Break || rotationMode.get() == RotationMode.Both) {
            if (!rotateToCrystal(crystal)) return false;
        }

        // Send attack packet directly for maximum speed
        mc.player.networkHandler.sendPacket(PlayerInteractEntityC2SPacket.attack(crystal, mc.player.isSneaking()));

        // Update tracking
        crystalBreakAttempts.put(crystal.getId(),
            crystalBreakAttempts.getOrDefault(crystal.getId(), 0) + 1);
        crystalLastAttack.put(crystal.getId(), System.currentTimeMillis());

        return true;
    }

    private boolean performStandardBreak(EndCrystalEntity crystal) {
        // Rotate if needed
        if (rotationMode.get() == RotationMode.Break || rotationMode.get() == RotationMode.Both) {
            if (!rotateToCrystal(crystal)) return false;
        }

        // Standard attack through interaction manager
        mc.interactionManager.attackEntity(mc.player, crystal);

        // Update tracking
        crystalBreakAttempts.put(crystal.getId(),
            crystalBreakAttempts.getOrDefault(crystal.getId(), 0) + 1);
        crystalLastAttack.put(crystal.getId(), System.currentTimeMillis());

        return true;
    }

    private boolean rotateToCrystal(EndCrystalEntity crystal) {
        Vec3d crystalPos = crystal.getPos();
        Vec3d playerPos = mc.player.getEyePos();
        Vec3d direction = crystalPos.subtract(playerPos).normalize();

        float yaw = (float) Math.toDegrees(Math.atan2(direction.z, direction.x)) - 90f;
        float pitch = (float) -Math.toDegrees(Math.asin(direction.y));

        // Check if rotation is within speed limits
        float currentYaw = mc.player.getYaw();
        float currentPitch = mc.player.getPitch();

        float yawDiff = Math.abs(yaw - currentYaw);
        float pitchDiff = Math.abs(pitch - currentPitch);

        // Normalize yaw difference
        if (yawDiff > 180) yawDiff = 360 - yawDiff;

        float maxRotation = rotationSpeed.get().floatValue();

        // Apply rotation speed limits
        if (yawDiff > maxRotation) {
            yaw = currentYaw + Math.signum(yaw - currentYaw) * maxRotation;
        }
        if (pitchDiff > maxRotation) {
            pitch = currentPitch + Math.signum(pitch - currentPitch) * maxRotation;
        }

        Rotations.rotate(yaw, pitch);
        return true;
    }

    // === PLACEMENT PATTERN METHODS ===
    private List<BlockPos> arrangeSurrounding(List<BlockPos> positions, BlockPos target, int count) {
        List<BlockPos> arranged = new ArrayList<>();
        if (positions.isEmpty()) return arranged;

        // Sort positions by distance from target
        positions.sort((a, b) -> Double.compare(
            a.getSquaredDistance(target),
            b.getSquaredDistance(target)
        ));

        // Select positions in a surrounding pattern
        BlockPos[] offsets = {
            target.add(1, 0, 0), target.add(-1, 0, 0),
            target.add(0, 0, 1), target.add(0, 0, -1),
            target.add(1, 0, 1), target.add(-1, 0, -1),
            target.add(1, 0, -1), target.add(-1, 0, 1)
        };

        for (BlockPos offset : offsets) {
            if (arranged.size() >= count) break;
            BlockPos closest = findClosestPosition(positions, offset);
            if (closest != null && !arranged.contains(closest)) {
                arranged.add(closest);
            }
        }

        return arranged;
    }

    private List<BlockPos> arrangeLinear(List<BlockPos> positions, BlockPos target, int count) {
        List<BlockPos> arranged = new ArrayList<>();
        if (positions.isEmpty()) return arranged;

        Vec3d playerPos = mc.player.getPos();
        Vec3d targetPos = Vec3d.ofCenter(target);
        Vec3d direction = targetPos.subtract(playerPos).normalize();

        // Find positions along the line from player to target
        positions.sort((a, b) -> {
            Vec3d aVec = Vec3d.ofCenter(a);
            Vec3d bVec = Vec3d.ofCenter(b);
            double aDot = aVec.subtract(playerPos).normalize().dotProduct(direction);
            double bDot = bVec.subtract(playerPos).normalize().dotProduct(direction);
            return Double.compare(bDot, aDot);
        });

        for (int i = 0; i < Math.min(count, positions.size()); i++) {
            arranged.add(positions.get(i));
        }

        return arranged;
    }

    private List<BlockPos> arrangeCross(List<BlockPos> positions, BlockPos target, int count) {
        List<BlockPos> arranged = new ArrayList<>();
        if (positions.isEmpty()) return arranged;

        // Cross pattern around target
        BlockPos[] crossOffsets = {
            target.add(2, 0, 0), target.add(-2, 0, 0),
            target.add(0, 0, 2), target.add(0, 0, -2),
            target.add(1, 0, 0) // Center if needed
        };

        for (BlockPos offset : crossOffsets) {
            if (arranged.size() >= count) break;
            BlockPos closest = findClosestPosition(positions, offset);
            if (closest != null && !arranged.contains(closest)) {
                arranged.add(closest);
            }
        }

        return arranged;
    }

    private List<BlockPos> arrangeDiamond(List<BlockPos> positions, BlockPos target, int count) {
        List<BlockPos> arranged = new ArrayList<>();
        if (positions.isEmpty()) return arranged;

        // Diamond pattern
        BlockPos[] diamondOffsets = {
            target.add(2, 0, 0), target.add(-2, 0, 0),
            target.add(0, 0, 2), target.add(0, 0, -2),
            target.add(1, 0, 1), target.add(-1, 0, -1),
            target.add(1, 0, -1), target.add(-1, 0, 1)
        };

        for (BlockPos offset : diamondOffsets) {
            if (arranged.size() >= count) break;
            BlockPos closest = findClosestPosition(positions, offset);
            if (closest != null && !arranged.contains(closest)) {
                arranged.add(closest);
            }
        }

        return arranged;
    }

    private List<BlockPos> arrangeTrap(List<BlockPos> positions, BlockPos target, int count) {
        List<BlockPos> arranged = new ArrayList<>();
        if (positions.isEmpty()) return arranged;

        // Trap formation - prioritize positions that block escape routes
        Vec3d targetVel = currentTarget.getVelocity();
        BlockPos escapeDirection = target.add(
            (int) Math.signum(targetVel.x),
            0,
            (int) Math.signum(targetVel.z)
        );

        // Block escape route first
        BlockPos closest = findClosestPosition(positions, escapeDirection);
        if (closest != null) {
            arranged.add(closest);
        }

        // Then surround
        BlockPos[] trapOffsets = {
            target.add(1, 0, 0), target.add(-1, 0, 0),
            target.add(0, 0, 1), target.add(0, 0, -1)
        };

        for (BlockPos offset : trapOffsets) {
            if (arranged.size() >= count) break;
            BlockPos pos = findClosestPosition(positions, offset);
            if (pos != null && !arranged.contains(pos)) {
                arranged.add(pos);
            }
        }

        return arranged;
    }

    // === AutoGap Helper Methods ===

    private boolean shouldGapEat() {
        if (mc.player == null) return false;
        float health = mc.player.getHealth() + mc.player.getAbsorptionAmount();
        return health <= gapHealthThreshold.get();
    }

    private boolean isGapItem(ItemStack stack) {
        if (stack == null || stack.isEmpty()) return false;
        Item item = stack.getItem();
        return item == Items.GOLDEN_APPLE || item == Items.ENCHANTED_GOLDEN_APPLE;
    }

    private int findGapSlot() {
        // Prefer enchanted golden apple if available
        for (int i = 0; i < 9; i++) {
            ItemStack stack = mc.player.getInventory().getStack(i);
            if (stack.getItem() == Items.ENCHANTED_GOLDEN_APPLE) return i;
        }
        // Otherwise, normal golden apple
        for (int i = 0; i < 9; i++) {
            ItemStack stack = mc.player.getInventory().getStack(i);
            if (stack.getItem() == Items.GOLDEN_APPLE) return i;
        }
        return -1;
    }

    private void stopGapEating() {
        changeGapSlot(gapPrevSlot);
        mc.options.useKey.setPressed(false);
        gapEating = false;
    }

    private void changeGapSlot(int slot) {
        if (slot < 0 || slot > 8) return;
        InvUtils.swap(slot, false);
    }

    private void gapEat() {
        changeGapSlot(gapSlot);
        mc.options.useKey.setPressed(true);
        gapEating = true;
    }

    private void startGapEating() {
        gapPrevSlot = mc.player.getInventory().selectedSlot;
        gapEat();
    }

    private BlockPos findClosestPosition(List<BlockPos> positions, BlockPos target) {
        BlockPos closest = null;
        double minDistance = Double.MAX_VALUE;

        for (BlockPos pos : positions) {
            double distance = pos.getSquaredDistance(target);
            if (distance < minDistance) {
                minDistance = distance;
                closest = pos;
            }
        }

        return closest;
    }
}
