// NotificationConfig: per-notification configuration
package dev.journey.Skylandia.utils;

public class NotificationConfig {
    private boolean enabled;
    private String outputType; // "toast", "chat", etc.
    private long minIntervalMs; // Minimum interval between notifications of this type

    private long lastNotified = 0;

    public NotificationConfig(boolean enabled, String outputType, long minIntervalMs) {
        this.enabled = enabled;
        this.outputType = outputType;
        this.minIntervalMs = minIntervalMs;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getOutputType() {
        return outputType;
    }

    public void setOutputType(String outputType) {
        this.outputType = outputType;
    }

    public long getMinIntervalMs() {
        return minIntervalMs;
    }

    public void setMinIntervalMs(long minIntervalMs) {
        this.minIntervalMs = minIntervalMs;
    }

    public boolean canNotify() {
        long now = System.currentTimeMillis();
        if (now - lastNotified >= minIntervalMs) {
            lastNotified = now;
            return true;
        }
        return false;
    }
}