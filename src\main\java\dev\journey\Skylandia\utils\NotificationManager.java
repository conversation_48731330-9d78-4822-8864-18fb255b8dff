package dev.journey.Skylandia.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;

public class NotificationManager {
    private static final ConcurrentLinkedQueue<Notification> notifications = new ConcurrentLinkedQueue<>();
    private static final int MAX_NOTIFICATIONS = 100;

    public static void addNotification(Notification notification) {
        if (notifications.size() >= MAX_NOTIFICATIONS) {
            notifications.poll();
        }
        notifications.offer(notification);
    }

    public static void dispatchNotification(Notification notification, String handlerType) {
        NotificationConfig config = NotificationConfigManager.getOrDefault(notification.getType());
        if (!config.isEnabled() || !config.canNotify()) return;
        addNotification(notification);
        String output = handlerType != null ? handlerType : config.getOutputType();
        NotificationRegistry.dispatch(notification, output);
    }

    public static List<Notification> getNotifications() {
        return new ArrayList<>(notifications);
    }

    public static void clearNotifications() {
        notifications.clear();
    }

    public static List<Notification> getNotificationsByPriority(NotificationPriority priority) {
        List<Notification> result = new ArrayList<>();
        for (Notification notification : notifications) {
            if (notification.getPriority() == priority) {
                result.add(notification);
            }
        }
        return result;
    }

    public static int getNotificationCount() {
        return notifications.size();
    }
}