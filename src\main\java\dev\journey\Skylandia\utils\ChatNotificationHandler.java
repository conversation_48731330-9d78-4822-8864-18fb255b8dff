// ChatNotificationHandler: displays notifications in chat
package dev.journey.Skylandia.utils;

import meteordevelopment.meteorclient.utils.player.ChatUtils;

public class ChatNotificationHandler implements NotificationHandler {
    @Override
    public String getHandlerType() {
        return "chat";
    }

    @Override
    public void handle(Notification notification) {
        ChatUtils.info("[NOTIFY] " + notification.getMessage());
    }
}