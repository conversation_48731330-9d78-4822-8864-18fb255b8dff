// NotificationConfigManager: manages per-notification configs
package dev.journey.Skylandia.utils;

import java.util.HashMap;
import java.util.Map;

public class NotificationConfigManager {
    private static final Map<String, NotificationConfig> configMap = new HashMap<>();

    public static void setConfig(String notificationType, NotificationConfig config) {
        configMap.put(notificationType, config);
    }

    public static NotificationConfig getConfig(String notificationType) {
        return configMap.get(notificationType);
    }

    public static NotificationConfig getOrDefault(String notificationType) {
        return configMap.computeIfAbsent(notificationType,
            t -> new NotificationConfig(true, "toast", 0));
    }
}