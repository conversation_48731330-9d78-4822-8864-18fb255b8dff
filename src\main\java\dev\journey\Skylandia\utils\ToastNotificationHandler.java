// ToastNotificationHandler: displays notifications as toasts
package dev.journey.Skylandia.utils;

import meteordevelopment.meteorclient.utils.player.ChatUtils;

public class ToastNotificationHandler implements NotificationHandler {
    @Override
    public String getHandlerType() {
        return "toast";
    }

    @Override
    public void handle(Notification notification) {
        ChatUtils.info("[TOAST] " + notification.getMessage());
    }
}