/**
 * Extending the Notification System:
 * - To add a new notification type: implement the Notification interface or extend DemonCrystalNotification.
 * - To add a new output module: implement NotificationHandler and register it in NotificationRegistry.
 * - To configure: use NotificationSettings to enable/disable, set output type, and set frequency per type.
 * - To trigger: call NotificationManager.dispatchNotification with your notification and (optionally) handler type.
 */
// Generic Notification interface for extensible notification system
package dev.journey.Skylandia.utils;

public interface Notification {
    String getType();
    String getMessage();
    NotificationPriority getPriority();
    long getTimestamp();
}