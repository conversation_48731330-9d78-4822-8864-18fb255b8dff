// NotificationRegistry for handler registration and notification dispatch
package dev.journey.Skylandia.utils;

import java.util.HashMap;
import java.util.Map;

public class NotificationRegistry {
    private static final Map<String, NotificationHandler> handlers = new HashMap<>();

    public static void registerHandler(NotificationHandler handler) {
        handlers.put(handler.getHandlerType(), handler);
    }

    public static NotificationHandler getHandler(String type) {
        return handlers.get(type);
    }

    public static void dispatch(Notification notification, String handlerType) {
        NotificationHandler handler = handlers.get(handlerType);
        if (handler != null) {
            handler.handle(notification);
        }
    }
}