package dev.journey.Skylandia.modules.render;

import dev.journey.Skylandia.Skylandia;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.renderer.text.TextRenderer;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.render.NametagUtils;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.util.math.Vec3d;
import org.joml.Vector3d;

import java.util.*;

public class RoleTags extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgRender = settings.createGroup("Render");
    private final SettingGroup sgRoles = settings.createGroup("Roles");
    private final SettingGroup sgPosition = settings.createGroup("Position");

    // Position settings
    private final Setting<Double> xOffset = sgPosition.add(new DoubleSetting.Builder()
        .name("x-offset")
        .description("Horizontal offset for the role tag.")
        .defaultValue(0.0)
        .min(-500.0)
        .max(500.0)
        .sliderRange(-500.0, 500.0)
        .build()
    );

    private final Setting<Double> yOffset = sgPosition.add(new DoubleSetting.Builder()
        .name("y-offset")
        .description("Vertical offset for the role tag.")
        .defaultValue(15.0)
        .min(-500.0)
        .max(500.0)
        .sliderRange(-500.0, 500.0)
        .build()
    );

    // Player role settings
    private final Setting<String> recruitPlayers = sgRoles.add(new StringSetting.Builder()
        .name("recruit-players")
        .description("Recruit players (comma separated)")
        .defaultValue("")
        .build()
    );

    private final Setting<String> memberPlayers = sgRoles.add(new StringSetting.Builder()
        .name("member-players")
        .description("Member players (comma separated)")
        .defaultValue("")
        .build()
    );

    private final Setting<String> elitePlayers = sgRoles.add(new StringSetting.Builder()
        .name("elite-players")
        .description("Elite players (comma separated)")
        .defaultValue("")
        .build()
    );

    private final Setting<String> adminPlayers = sgRoles.add(new StringSetting.Builder()
        .name("admin-players")
        .description("Admin players (comma separated)")
        .defaultValue("")
        .build()
    );

    private final Setting<String> ownerPlayers = sgRoles.add(new StringSetting.Builder()
        .name("owner-players")
        .description("Owner players (comma separated)")
        .defaultValue("")
        .build()
    );

    private final Setting<Boolean> showUnlisted = sgRoles.add(new BoolSetting.Builder()
        .name("show-unlisted")
        .description("Whether to show tags for unlisted players")
        .defaultValue(false)
        .build()
    );

    // Role colors
    private final Setting<SettingColor> unlistedColor = sgRoles.add(new ColorSetting.Builder()
        .name("unlisted-color")
        .description("Color for unlisted players")
        .defaultValue(new SettingColor(150, 150, 150))
        .build()
    );

    private final Setting<SettingColor> recruitColor = sgRoles.add(new ColorSetting.Builder()
        .name("recruit-color")
        .description("Color for recruit players")
        .defaultValue(new SettingColor(85, 255, 85))
        .build()
    );

    private final Setting<SettingColor> memberColor = sgRoles.add(new ColorSetting.Builder()
        .name("member-color")
        .description("Color for member players")
        .defaultValue(new SettingColor(85, 85, 255))
        .build()
    );

    private final Setting<SettingColor> eliteColor = sgRoles.add(new ColorSetting.Builder()
        .name("elite-color")
        .description("Color for elite players")
        .defaultValue(new SettingColor(255, 85, 85))
        .build()
    );

    private final Setting<SettingColor> adminColor = sgRoles.add(new ColorSetting.Builder()
        .name("admin-color")
        .description("Color for admin players")
        .defaultValue(new SettingColor(255, 170, 0))
        .build()
    );

    private final Setting<SettingColor> ownerColor = sgRoles.add(new ColorSetting.Builder()
        .name("owner-color")
        .description("Color for owner players")
        .defaultValue(new SettingColor(255, 0, 255))
        .build()
    );

    // Render settings
    private final Setting<Integer> fixedWidth = sgRender.add(new IntSetting.Builder()
        .name("fixed-width")
        .description("Fixed width for all role tags in pixels")
        .defaultValue(80)
        .min(40)
        .max(120)
        .sliderRange(40, 120)
        .build()
    );

    private final Setting<Double> scale = sgGeneral.add(new DoubleSetting.Builder()
        .name("scale")
        .description("Scale of the role text.")
        .defaultValue(1.0)
        .min(0.1)
        .max(5.0)
        .sliderRange(0.1, 5.0)
        .build()
    );

    private final Setting<Boolean> shadow = sgRender.add(new BoolSetting.Builder()
        .name("shadow")
        .description("Render text with shadow")
        .defaultValue(true)
        .build()
    );

    // Internal variables
    private final Color background = new Color(50, 50, 50, 255);
    private final List<Entity> entityList = new ArrayList<>();
    private final Vector3d pos = new Vector3d();
    private final Set<String> recruitNames = new HashSet<>();
    private final Set<String> memberNames = new HashSet<>();
    private final Set<String> eliteNames = new HashSet<>();
    private final Set<String> adminNames = new HashSet<>();
    private final Set<String> ownerNames = new HashSet<>();

    public RoleTags() {
        super(Skylandia.Render, "role-tags", "Displays player roles near their nametag");
    }

    @Override
    public void onActivate() {
        updateRoleLists();
    }

    private void updateRoleLists() {
        recruitNames.clear();
        memberNames.clear();
        eliteNames.clear();
        adminNames.clear();
        ownerNames.clear();

        addNamesToSet(recruitPlayers.get(), recruitNames);
        addNamesToSet(memberPlayers.get(), memberNames);
        addNamesToSet(elitePlayers.get(), eliteNames);
        addNamesToSet(adminPlayers.get(), adminNames);
        addNamesToSet(ownerPlayers.get(), ownerNames);
    }

    private void addNamesToSet(String input, Set<String> set) {
        if (input == null || input.isEmpty()) return;

        String[] names = input.split(",");
        for (String name : names) {
            String trimmed = name.trim();
            if (!trimmed.isEmpty()) {
                set.add(trimmed.toLowerCase());
            }
        }
    }

    private String getPlayerRole(PlayerEntity player) {
        String name = player.getName().getString().toLowerCase();

        if (ownerNames.contains(name)) return "Owner";
        if (adminNames.contains(name)) return "Admin";
        if (eliteNames.contains(name)) return "Elite";
        if (memberNames.contains(name)) return "Member";
        if (recruitNames.contains(name)) return "Recruit";
        return "Unlisted";
    }

    private boolean shouldRenderRole(PlayerEntity player) {
        String role = getPlayerRole(player);
        return !role.equals("Unlisted") || showUnlisted.get();
    }

    private Color getRoleColor(String role) {
        return switch (role) {
            case "Owner" -> ownerColor.get();
            case "Admin" -> adminColor.get();
            case "Elite" -> eliteColor.get();
            case "Member" -> memberColor.get();
            case "Recruit" -> recruitColor.get();
            default -> unlistedColor.get();
        };
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        updateRoleLists();
    }

    @EventHandler
    private void onRender3D(Render3DEvent event) {
        if (mc.world == null || mc.player == null) return;

        entityList.clear();
        for (Entity entity : mc.world.getEntities()) {
            if (entity instanceof PlayerEntity player && entity != mc.player) {
                if (shouldRenderRole(player)) {
                    entityList.add(entity);
                }
            }
        }

        for (Entity entity : entityList) {
            if (entity instanceof PlayerEntity player) {
                renderRoleTag(event, player);
            }
        }
    }

    private void renderRoleTag(Render3DEvent event, PlayerEntity player) {
        String role = getPlayerRole(player);
        Color roleColor = getRoleColor(role);
        String roleText = "[" + role + "]";

        // Calculate position above player
        double x = player.prevX + (player.getX() - player.prevX) * event.tickDelta;
        double y = player.prevY + (player.getY() - player.prevY) * event.tickDelta;
        double z = player.prevZ + (player.getZ() - player.prevZ) * event.tickDelta;

        // Add player height and offset
        y += player.getEyeHeight(player.getPose()) + 0.5;

        pos.set(x, y, z);

        // Convert 3D position to 2D screen coordinates
        if (!NametagUtils.to2D(pos, scale.get())) return;

        // Apply offsets
        pos.x += xOffset.get();
        pos.y += yOffset.get();

        // Get text dimensions
        TextRenderer textRenderer = TextRenderer.get();
        double textWidth = textRenderer.getWidth(roleText, shadow.get());
        double textHeight = textRenderer.getHeight(shadow.get());

        // Calculate background dimensions
        double width = Math.max(textWidth, fixedWidth.get());
        double height = textHeight + 4;
        double widthHalf = width / 2.0;
        double heightHalf = height / 2.0;

        // Render background using 3D renderer
        event.renderer.quad(
            pos.x - widthHalf, pos.y - heightHalf, 0,
            pos.x + widthHalf, pos.y - heightHalf, 0,
            pos.x + widthHalf, pos.y + heightHalf, 0,
            pos.x - widthHalf, pos.y + heightHalf, 0,
            background
        );

        // Render text centered
        textRenderer.render(
            roleText,
            pos.x - textWidth / 2.0,
            pos.y - textHeight / 2.0,
            roleColor,
            shadow.get()
        );
    }
}