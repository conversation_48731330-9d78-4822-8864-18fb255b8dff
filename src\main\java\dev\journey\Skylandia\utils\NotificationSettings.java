// NotificationSettings: user-facing config interface for notifications
package dev.journey.Skylandia.utils;

public class NotificationSettings {
    public static void enableNotification(String type, boolean enabled) {
        NotificationConfig config = NotificationConfigManager.getOrDefault(type);
        config.setEnabled(enabled);
        NotificationConfigManager.setConfig(type, config);
    }

    public static void setOutputType(String type, String outputType) {
        NotificationConfig config = NotificationConfigManager.getOrDefault(type);
        config.setOutputType(outputType);
        NotificationConfigManager.setConfig(type, config);
    }

    public static void setFrequency(String type, long minIntervalMs) {
        NotificationConfig config = NotificationConfigManager.getOrDefault(type);
        config.setMinIntervalMs(minIntervalMs);
        NotificationConfigManager.setConfig(type, config);
    }

    public static NotificationConfig getConfig(String type) {
        return NotificationConfigManager.getOrDefault(type);
    }
}