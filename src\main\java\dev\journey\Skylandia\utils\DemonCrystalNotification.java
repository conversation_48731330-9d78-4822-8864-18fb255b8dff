// DemonCrystalNotification: notification for DemonCrystal events
package dev.journey.Skylandia.utils;

public class DemonCrystalNotification implements Notification {
    private final String type;
    private final String message;
    private final NotificationPriority priority;
    private final long timestamp;

    public DemonCrystalNotification(String type, String message, NotificationPriority priority) {
        this.type = type;
        this.message = message;
        this.priority = priority;
        this.timestamp = System.currentTimeMillis();
    }

    @Override
    public String getType() {
        return type;
    }

    @Override
    public String getMessage() {
        return message;
    }

    @Override
    public NotificationPriority getPriority() {
        return priority;
    }

    @Override
    public long getTimestamp() {
        return timestamp;
    }
}