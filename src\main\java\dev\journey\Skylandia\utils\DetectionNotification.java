package dev.journey.Skylandia.utils;

import dev.journey.Skylandia.modules.exploration.detection.DetectionResult;

public class DetectionNotification implements Notification {
    private final DetectionResult result;
    private final NotificationPriority priority;
    private final long timestamp;

    public DetectionNotification(DetectionResult result, NotificationPriority priority) {
        this.result = result;
        this.priority = priority;
        this.timestamp = System.currentTimeMillis();
    }

    public DetectionResult getResult() {
        return result;
    }

    @Override
    public NotificationPriority getPriority() {
        return priority;
    }

    @Override
    public long getTimestamp() {
        return timestamp;
    }

    @Override
    public String getType() {
        return "Detection";
    }

    @Override
    public String getMessage() {
        return result != null ? result.toString() : "Detection event";
    }
}